{"sblevel_colours": {"1": "GRAY", "40": "WHITE", "80": "YELLOW", "120": "GREEN", "160": "DARK_GREEN", "200": "AQUA", "240": "DARK_AQUA", "280": "BLUE", "320": "LIGHT_PURPLE", "360": "DARK_PURPLE", "400": "GOLD", "440": "RED", "480": "DARK_RED"}, "category_xp": {"core_task": 14235, "dungeon_task": 2760, "essence_shop_task": 1310, "slaying_task": 10190, "skill_related_task": 5883, "miscellaneous_task": 2381, "story_task": 497, "event_task": 625}, "core_task": {"skill_level_up": 7800, "fairy_souls": 490, "collections": 2696, "craft_minions": 2834, "fast_travel_unlocked": 225, "collections_xp": 4, "accessory_bag_xp": 1, "pet_score_xp": 3, "fairy_souls_xp": 10, "bank_upgrades": 200, "bank_upgrades_xp": {"BANK_UPGRADE_GOLD": 20, "BANK_UPGRADE_DELUXE": 25, "BANK_UPGRADE_SUPER_DELUXE": 30, "BANK_UPGRADE_PREMIER": 35, "BANK_UPGRADE_LUXURIOUS": 40, "BANK_UPGRADE_PALATIAL": 50}, "fast_travel_unlocked_xp": 15}, "dungeon_task": {"catacombs_level_up": 1220, "class_level_up": 1000, "complete_dungeon": 540, "class_xp": 4, "complete_catacombs": [20, 20, 20, 20, 20, 30, 30, 30], "complete_master_catacombs": 50}, "essence_shop_task": {"essence_dragon_shop": 185, "essence_ice_shop": 121, "essence_spider_shop": 126, "essence_undead_shop": 128, "essence_wither_shop": 142, "essence_crimson_shop": 194, "essence_diamond_shop": 226, "essence_gold_shop": 188, "essence_shop_xp": [2, 2, 3, 5, 7, 8, 8, 8, 9, 10]}, "slaying_task": {"slayer_level_up": 3825, "boss_collections": 1015, "slay_dragons": 200, "defeat_slayers": 1050, "defeat_kuudra": 300, "defeat_arachne": 60, "mythological_kills": 100, "bestiary_progress": 3640, "mythological_kills_xp": 1, "slayer_level_up_xp": [15, 25, 35, 50, 75, 100, 125, 150, 150], "defeat_arachne_xp": [20, 40], "defeat_kuudra_xp": [20, 40, 60, 80, 100], "defeat_slayers_xp": [25, 25, 50, 50, 75], "slay_dragons_xp": {"superior_dragon": 50, "old_dragon": 25, "young_dragon": 25, "wise_dragon": 25, "protector_dragon": 25, "unstable_dragon": 25, "strong_dragon": 25}, "boss_collections_xp": {"dungeon_collection_xp": [15, 15, 15, 25, 25, 25, 25], "kuudra_collection_xp": [10, 15, 20, 25, 30]}, "bestiary_family_xp": 1, "bestiary_milestone_xp": 10}, "miscellaneous_task": {"reaper_peppers": 50, "the_dojo": 425, "harp_songs": 236, "abiphone_contacts": 510, "personal_bank_upgrades": 110, "community_shop_upgrades": 120, "timecharm": 800, "unlocking_relays": 45, "consumable_items": 135, "mcgrubber_burger": 25, "metaphysical_serum": 15, "refined_jyrre": 10, "wriggling_larva": 25, "refined_dark_cacao_truffles": 10, "unlocking_powers_xp": 15, "reaper_peppers_xp": 10, "accessory_bag_upgrades_xp": 2, "the_dojo_xp": [20, 30, 50, 75, 100, 150], "harp_songs_xp": 4, "abiphone_contacts_xp": 10, "personal_bank_upgrades_xp": [25, 35, 50], "accessory_bag_upgrades": {"2": 3, "6": 9, "9": 15, "10": 21, "11": 27, "12": 33, "13": 39, "14": 45, "15": 51, "16": 57}, "community_shop_upgrades_xp": 10, "harp_songs_names": {"SONG_HYMN_JOY": 4, "SONG_FRERE_JACQUES": 4, "SONG_AMAZING_GRACE": 4, "SONG_BRAHMS": 8, "SONG_HAPPY_BIRTHDAY": 8, "SONG_GREENSLEEVES": 8, "SONG_JEOPARDY": 16, "SONG_MINUET": 16, "SONG_JOY_WORLD": 16, "SONG_PURE_IMAGINATION": 28, "SONG_VIE_EN_ROSE": 28, "SONG_FIRE_AND_FLAMES": 48, "SONG_PACHELBEL": 48}, "community_shop_upgrades_max": {"island_size": 1, "minion_slots": 5, "guests_count": 1, "coins_allowance": 5}, "timecharm_xp": 100, "unlocking_relays_xp": 5, "metaphysical_serum_xp": 5, "mcgrubber_burger_xp": 5, "refined_jyrre_xp": 2, "wriggling_larva_xp": 5, "refined_dark_cacao_truffles_xp": 2}, "skill_related_task": {"mining": {"mining": 3848, "hotm": 2818, "commission_milestone": 255, "potm": 1000, "rock_milestone": 100, "crystal_nucleus": 200, "fossil_research": 80, "hotm_xp": [35, 45, 60, 75, 90, 110, 130, 180, 210, 240], "commission_milestone_xp": [20, 30, 30, 50, 50, 75], "potm_xp": [25, 35, 50, 65, 75, 100, 125, 150, 175, 200], "rock_milestone_required": [2500, 7500, 20000, 100000, 250000], "rock_milestone_xp": 20, "crystal_nucleus_xp": 4, "fossil_research_xp": 10}, "farming": {"farming": 250, "anita_shop_upgrades": 250, "anita_shop_upgrades_xp": 10}, "fishing": {"fishing": 1180, "trophy_fish": 1080, "dolphin_milestone": 100, "trophy_fish_xp": [4, 8, 16, 32], "dolphin_milestone_required": [250, 1000, 2500, 5000, 10000], "dolphin_milestone_xp": 20}}, "story_task": {"complete_objectives": 140, "rift_guide": 357, "complete_objectives_xp": 5, "rift_guide_xp": 3, "complete_objectives_names": ["explore_hub", "talk_to_lumberjack", "talk_to_fisherman_2", "kill_danger_mobs", "talk_to_guber_1", "talk_to_farmer", "talk_to_librarian", "explore_village", "complete_the_woods_race_4", "increase_foraging_skill_5", "public_island", "talk_to_lazy_miner", "increase_farming_skill_5", "talk_to_farmhand_1", "mine_coal", "talk_to_gulliver_1", "complete_the_chicken_race_4", "complete_the_end_race_4", "talk_to_gustave_1", "talk_to_banker", "help_elle", "give_sam_wheat", "unlock_crop_plot", "clean_crop_plot", "serve_first_customer", "talk_to_redstone_miner_5", "give_pickaxe_lapis_miner", "go_to_base_camp"], "rift_guide_names": ["rift_accessory_1", "rift_accessory_2", "rift_accessory_3", "rift_accessory_4", "rift_accessory_5", "rift_accessory_6", "rift_accessory_7", "rift_back2basics", "rift_barry", "rift_beanstalk", "rift_bid_shen", "rift_boat_1", "rift_boat_2", "rift_boat_3", "rift_boat_4", "rift_boat_5", "rift_bughunter", "rift_buttons_soul", "rift_cake_house_1", "rift_cake_house_2", "rift_carrots", "rift_castle_balloons", "rift_castle_flowerpot", "rift_castle_phantom", "rift_castle_standing", "rift_cave_1_craft_pickaxe", "rift_cave_2_juice_piece", "rift_cave_3_max_suit", "rift_cave_4_craft_timecharm", "rift_cave_5_unlock_collection", "rift_cave_flowers", "rift_cave_wall", "rift_complete_mirrorverse", "rift_complete_okron", "rift_dead_cat_1", "rift_dead_cat_2", "rift_dead_cat_3", "rift_dead_cat_4", "rift_dead_cat_5", "rift_dead_cat_6", "rift_dead_cat_7", "rift_dead_cat_8", "rift_dead_cat_9", "rift_deadgehog_helmet", "rift_defeat_bacte", "rift_defeat_leech_supreme", "rift_detective", "rift_disinfestor", "rift_dolphin_soul", "rift_dreadfarm_balloons", "rift_dreadfarm_flowerpot", "rift_enigma_bark", "rift_enigma_flower_pot_1", "rift_enigma_jump_pad_1", "rift_enigma_original", "rift_enigma_phantom", "rift_enigma_pressure_water", "rift_enigma_silk_1", "rift_enigma_two_plates", "rift_enigma_wither_cage", "rift_eye_1", "rift_eye_2", "rift_eye_3", "rift_eye_4", "rift_eye_5", "rift_eye_6", "rift_eye_7", "rift_fairylosopher_soul", "rift_find_shen", "rift_horsing_around", "rift_hot_dog", "rift_inverted_sirius", "<PERSON>_jacquelle", "rift_kat_soul", "rift_lagoon_parkour", "rift_lagoon_shop", "rift_lonely_dave", "rift_long_shrooms", "rift_mole", "rift_mountain_balloons", "rift_mountain_close_breach", "rift_mountain_eleanor_0", "rift_mountain_eleanor_1", "rift_mountain_eleanor_2", "rift_mountain_eleanor_3", "rift_mountain_eleanor_4", "rift_mountain_flowerpot", "rift_mountain_ikrus", "rift_mountain_leap_of_faith", "rift_mountain_phantom", "rift_mountain_sungecko_perks", "rift_mountain_ubik_averages", "rift_mountain_ubik_boxes", "rift_mountain_ubik_tats", "rift_mountain_unlock_timite", "rift_mountain_walk_of_fame_wall", "rift_mountain_wizardman_trials", "rift_obtain_healing_melon", "rift_plaza_balloons", "rift_porhtal", "rift_rabbit_under_hat", "rift_reed", "rift_reverse_murder_part_two", "rift_rocks_enigma", "rift_scammer", "rift_seraphine", "rift_seymour", "rift_shania", "rift_silk_2", "rift_silk_castle", "rift_talk_to_enigma", "rift_tel_kar", "rift_threebrothers", "rift_trafficker", "rift_two_player_soul", "rift_unhinged_kloon", "rift_village_glass", "rift_village_phantom", "rift_west_parkour"]}, "event_task": {"mining_fiesta": 200, "fishing_festival": 100, "spooky_festival": 225, "jacob_farming_contest": 100, "spooky_festival_xp": {"SPOOKY_FESTIVAL_WOOD": 10, "SPOOKY_FESTIVAL_STONE": 20, "SPOOKY_FESTIVAL_IRON": 30, "SPOOKY_FESTIVAL_GOLD": 40, "SPOOKY_FESTIVAL_DIAMOND": 50, "SPOOKY_FESTIVAL_EMERALD": 75}, "jacob_farming_contest_xp": 10}}