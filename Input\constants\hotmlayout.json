{"prelude": ["(defun min (l r) (if (lt l r) l r))", "(defun max (l r) (if (lt l r) r l))", "(defun round-decimals (number places) (/ (round (* number (pow 10 places))) (pow 10 places)))", "(defun id (x) (if true x x))", "(defun npi (level0 maxLevel) (if (= level0 0) :COAL (if (= level0 maxLevel) :DIAMOND :EMERALD)))", "(defun api (level0) (if (= level0 0) :COAL_BLOCK :EMERALD_BLOCK))"], "hotm": {"powders": {"GLACITE": {"costLine": "§7Cost: §b{cost} Glacite Powder"}, "GEMSTONE": {"costLine": "§7Cost: §d{cost} Gemstone Powder"}, "MITHRIL": {"costLine": "§7Cost: §2{cost} <PERSON><PERSON><PERSON>"}}, "perks": {"mining_speed": {"name": "Mining Speed", "x": 3, "y": 9, "maxLevel": 50, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3)", "stat": "(* level 20)", "lore": ["§7Grants §a+§a{stat} §6⸕ Mining Speed§7."]}, "mining_speed_boost": {"name": "Mining Speed Boost", "x": 1, "y": 8, "maxLevel": 1, "powder": "MITHRIL", "item": "(api level0)", "cost": "", "statBoost": "(if (lt potm 2) \"200\" \"250\")", "statDuration": "(if (lt potm 2) \"10\" \"15\")", "lore": ["", "§6Pickaxe Ability: Mining Speed Boost", "§7Grants §a+§a{statBoost}% §6⸕ Mining Speed §7for", "§7§a{statDuration}s§7.", "§8Cooldown: §a120s"]}, "precision_mining": {"name": "Precision Mining", "x": 2, "y": 8, "maxLevel": 1, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "0", "lore": ["§7When Mining §6Ores§7 or §8Dwarven Metals§7,", "§7a particle target appears on the", "§7block that increases your §6⸕ Mining", "§6Speed §7by §a30% §7when aiming at it."]}, "mining_fortune": {"name": "Mining Fortune", "x": 3, "y": 8, "maxLevel": 50, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.05)", "stat": "(* level 2)", "lore": ["§7Grants §a+§a{stat} §6☘ Mining Fortune§7."]}, "titanium_insanium": {"name": "Titanium Insanium", "x": 4, "y": 8, "maxLevel": 50, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.1)", "stat": "(round-decimals (+ 2 (* level 0.1)) 1)", "lore": ["§7When mining §2Mithril Ore§7, you have a", "§7§a{stat}% §7chance to convert the block", "§7into §fTitanium Ore§7."]}, "pickaxe_toss": {"name": "Pickobulus", "x": 5, "y": 8, "maxLevel": 1, "powder": "MITHRIL", "item": "(api level0)", "cost": "", "stat": "(if (lt potm 2) \"60\" \"50\")", "lore": ["", "§6Pickaxe Ability: Pickobulus", "§7Throw your pickaxe to create an", "§7explosion on impact, mining all ores", "§7within a §a2 §7block radius.", "§8Cooldown: §a{stat}s"]}, "random_event": {"name": "Luck of the Cave", "x": 1, "y": 7, "maxLevel": 45, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.07)", "stat": "(+ level 5)", "lore": ["§7Increases the chance for you to", "§7trigger rare occurrences in the", "§7§2Dwarven Mines §7by §a{stat}%§7.", "", "§7Rare occurrences include:", "§7 • §6Golden Goblins", "§7 • §5Fallen Stars", "§7 • §6Powder Ghasts"]}, "efficient_miner": {"name": "Efficient Miner", "x": 3, "y": 7, "maxLevel": 100, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.6)", "stat": "(* level 3)", "lore": ["§7Grants §e+{stat}▚ Mining Spread§7."]}, "forge_time": {"name": "Quick Forge", "x": 5, "y": 7, "maxLevel": 20, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "stat": "(if (lt level 20) (+ 10 (* level 0.5)) 30)", "lore": ["§7Decreases the time it takes to forge", "§7by §a{stat}%§7."]}, "daily_effect": {"name": "Sky Mall", "x": 0, "y": 6, "maxLevel": 1, "powder": "MITHRIL", "item": "(npi level0 maxLevel)", "cost": "0", "lore": ["§7Every SkyBlock day, you receive a", "§7random buff while on any §bMining", "§bIsland§7.", "", "§7Possible Buffs", "§8 ■ §7Gain §6+100⸕ Mining Speed§7.", "§8 ■ §7Gain §6+50☘ Mining Fortune§7.", "§8 ■ §7Gain §a+15% §7more Powder while mining.", "§8 ■ §7§a-20%§7 Pickaxe Ability cooldowns.", "§8 ■ §7§a10x §7chance to find <PERSON> and", "    §7<PERSON><PERSON><PERSON>.", "§8 ■ §7Gain §a5x §9Titanium §7drops."]}, "old_school": {"name": "Old-School", "x": 1, "y": 6, "maxLevel": 20, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "stat": "(* level 5)", "lore": ["§7Grants §6+{stat}☘ Ore Fortune§7."]}, "professional": {"name": "Professional", "x": 2, "y": 6, "maxLevel": 140, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.3)", "stat": "(+ (* level 5) 50)", "lore": ["§7Gain §a+§a{stat} §6⸕ Mining Speed§7 when mining", "§7Gemstones."]}, "mole": {"name": "<PERSON><PERSON>", "x": 3, "y": 6, "maxLevel": 200, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.2)", "stat": "(+ 50 (/ (* (- level 1) 350) 199))", "lore": ["§7Grants §e+{stat}▚ Mining Spread§7 when", "§7mining Hard Stone."]}, "fortunate": {"name": "<PERSON>em <PERSON>", "x": 4, "y": 6, "maxLevel": 20, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "stat": "(+ (* level 4) 20)", "lore": ["§7Grants §6+{stat}☘ Gemstone Fortune§7."]}, "mining_experience": {"name": "Seasoned Mineman", "x": 5, "y": 6, "maxLevel": 100, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.3)", "stat": "(round-decimals (+ 5 (* level 0.1)) 1)", "lore": ["§7Grants §3+{stat}☯ Mining Wisdom§7."]}, "front_loaded": {"name": "Front Loaded", "x": 6, "y": 6, "maxLevel": 1, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "0", "lore": ["§7§7Grants the following buffs for the", "§7first §a2,500 §dGemstones§7 you mine each", "§7day.", " ", " §8■ §d3x Gemstone Powder", " §8■ §6+150☘ Gemstone Fortune", " §8■ §6+250⸕ Mining Speed"]}, "daily_grind": {"name": "Daily Grind", "x": 1, "y": 5, "maxLevel": 1, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "0", "stat": "(* (floor hotm) 500)", "lore": ["§7§7Your first daily commission on each", "§7§bMining Island§7 grants §9+500 Powder§7,", "§7multiplied by your §5HOTM§7 level.", "", "§2Dwarven Mines§7: §a+{stat} §2Mithril Powder", "§5Crystal Hollows§7: §a+{stat} §dGemstone Powder", "§bGlacite Tunnels§7: §a+{stat} §bGlacite Powder"]}, "special_0": {"name": "Core of the Mountain", "x": 3, "y": 5, "maxLevel": 10, "powder": "(if (lt level 4) MITHRIL (if (lt level 8) GEMSTONE GLACITE))", "item": ":REDSTONE_BLOCK", "cost": "(list.at (list.new 0 50000 100000 200000 300000 400000 600000 750000 1000000 1250000 0) level)", "lore": [{"text": "§7§8+§c1 Pickaxe Ability Level", "onlyIf": "(gt level 0)"}, {"text": "§7§8+§a1 Forge Slot", "onlyIf": "(gt level 1)"}, {"text": "§7§8+§a1 Commission Slot", "onlyIf": "(gt level 2)"}, {"text": "§7§8+§21 Base Mithril Powder §7when mining §2Mithril§7.", "onlyIf": "(gt level 3)"}, {"text": "§8+§51 Token of the Mountain", "onlyIf": "(gt level 4)"}, {"text": "§7§8+§d2 Base Gemstone Powder §7when mining §dGemstones§7.", "onlyIf": "(gt level 5)"}, {"text": "§8+§51 Token of the Mountain", "onlyIf": "(gt level 6)"}, {"text": "§7§8+§b3 Base Glacite Powder §7when mining §bGlacite§7.", "onlyIf": "(gt level 7)"}, {"text": "§7§8+§a10% chance §7for §bGlacite Mineshafts §7to spawn.", "onlyIf": "(gt level 8)"}, {"text": "§8+§52 Token of the Mountain", "onlyIf": "(gt level 9)"}]}, "daily_powder": {"name": "Daily Powder", "x": 5, "y": 5, "maxLevel": 1, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "0", "stat": "(* (floor hotm) 500)", "lore": ["§7The first ore you mine each day", "§7grants §9+500 Powder§7, multiplied by", "§7your §5HOTM §7level.", "", "§2Mithril§7: §a+{stat} §2Mithril Powder", "§dGemstone§7: §a+{stat} §dGemstone Powder", "§bGlacite§7: §a+{stat} §bGlacite Powder"]}, "anomalous_desire": {"name": "Anomalous Desire", "x": 0, "y": 4, "maxLevel": 1, "powder": "GEMSTONE", "item": "(api level0)", "cost": "0", "stat": "(if (lt potm 2) \"30\" \"40\")", "statCooldown": "(if (lt potm 2) \"120\" \"110\")", "lore": ["§6Pickaxe Ability: Anomalous Desire", "§7Increases the chances of triggering", "§7rare occurrences by §a§a{stat}%§7 for §a30s§7.", "", "§7Rare occurrences include:", "§7§8 ■ §6Golden Goblins", "§7§8 ■ §5Fallen Stars", "§7§8 ■ §6Powder Ghasts", "§7§8 ■ §6Worms", "§7§8 ■ §bGlacite Mineshafts", "§8Cooldown: §a{statCooldown}s"]}, "blockhead": {"name": "Blockhead", "x": 1, "y": 4, "maxLevel": 20, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "stat": "(* level 5)", "lore": ["§7Grants §6+{stat}☘ Block Fortune§7."]}, "subterranean_fisher": {"name": "Subterranean Fisher", "x": 2, "y": 4, "maxLevel": 40, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.7)", "statFishingSpeed": "(+ (* level 0.5) 5)", "statSeaCreatureChance": "(+ (* level 0.1) 1)", "lore": ["§7§7Grants §b+{statFishingSpeed}☂ Fishing Speed§7 and §3+{statSeaCreatureChance}α", "§7§3Sea Creature Chance§7 while on §bMining", "§7§bIslands§7."]}, "keep_it_cool": {"name": "Keep It Cool", "x": 3, "y": 4, "maxLevel": 50, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.07)", "stat": "(* level 0.4)", "lore": ["§7Grants §c+{stat}♨ Heat Resistance§7."]}, "lonesome_miner": {"name": "Lonesome Miner", "x": 4, "y": 4, "maxLevel": 45, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.07)", "stat": "(+ (* level 0.5) 4.5)", "lore": ["§7Increases §c❁ Strength§7, §9☣ Crit", "§9Chance§7, §9☠ Crit Damage§7, §a❈ Defense§7,", "§7and §c❤ Health §7statistics gain by §a{stat}%", "§a§7while on §bMining Islands§7."]}, "great_explorer": {"name": "Great Explorer", "x": 5, "y": 4, "maxLevel": 20, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "statChance": "(+ (* level 4) 16)", "statLocks": "(round (+ (/ level 5) 1))", "lore": ["§7Boosts the chance to find treasure", "§7chests while mining in the §5Crystal", "§5Hollows §7by §a+§a{statChance}% §7and reduces the", "§7amount of locks on the chests by §a{statLocks}§7."]}, "maniac_miner": {"name": "Maniac Miner", "x": 6, "y": 4, "maxLevel": 1, "powder": "GEMSTONE", "item": "(api level0)", "cost": "", "stat": "(if (lt potm 2) \"5\" \"10\")", "statDuration": "(if (lt potm 2) \"25\" \"30\")", "lore": ["", "§6Pickaxe Ability: Maniac Miner", "§7Grants §2+1Ⓟ Breaking Power§7 and a", "§7stack of §6+{stat}☘ Mining Fortune §8(caps", "§8at 1000) §7per block broken for §a{statDuration}s§7.", "§7Each block broken consumes §b20 Mana§7.", "§8Cooldown: §a120s"]}, "powder_buff": {"name": "<PERSON><PERSON><PERSON>", "x": 3, "y": 3, "maxLevel": 50, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.2)", "stat": "(id level)", "lore": ["§7Gain §a+§a{stat}% §7more Powder from any", "§7source."]}, "mining_speed_2": {"name": "<PERSON><PERSON>", "x": 1, "y": 3, "maxLevel": 50, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.2)", "stat": "(* level 40)", "lore": ["§7Grants §a+§a{stat} §6⸕ Mining Speed§7."]}, "mining_fortune_2": {"name": "Fortunate Mineman", "x": 5, "y": 3, "maxLevel": 50, "powder": "GEMSTONE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.2)", "stat": "(* level 3)", "lore": ["§7Grants §a+§a{stat} §6☘ Mining Fortune§7."]}, "warm_hearted": {"name": "Warm Heart", "x": 4, "y": 2, "maxLevel": 50, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.1)", "stat": "(* level 0.4)", "lore": ["§7Grants §a+§a{stat} §b❄ Cold Resistance§7."]}, "miners_blessing": {"name": "Miner's Blessing", "x": 0, "y": 2, "maxLevel": 1, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "0", "lore": ["§7Grants §b+30✯ Magic Find§7 on all §bMining", "§bIslands§7."]}, "no_stone_unturned": {"name": "No Stone Unturned", "x": 1, "y": 2, "maxLevel": 50, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.05)", "stat": "(* level 0.5)", "lore": ["§7Increases your chances of finding a", "§7§9Suspicious Scrap §7when mining in a", "§7§bGlacite Mineshaft §7by §a§a{stat}%§7."]}, "strong_arm": {"name": "Strong Arm", "x": 2, "y": 2, "maxLevel": 100, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.3)", "stat": "(* level 5)", "lore": ["§7Gain §6+{stat}⸕ Mining Speed§7 when mining", "§7§6Dwarven Metals§7."]}, "steady_hand": {"name": "Steady Hand", "x": 3, "y": 2, "maxLevel": 100, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.6)", "stat": "(* level 0.1)", "lore": ["§7Grants §e+{stat}▚ Gemstone Spread§7 while", "§7in the §bGlacite Mineshafts§7."]}, "mineshaft_mayhem": {"name": "Mineshaft Mayhem", "x": 6, "y": 2, "maxLevel": 1, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "0", "lore": ["§7Every time you enter a §bGlacite", "§bMineshaft§7, you receive a random buff.", "", "§7Possible Buffs", "§8 ■ §a+5% §7chance to find a §9Suspicious Scrap§7.", "§8 ■ §7Gain §a+100 §6☘ Mining Fortune§7", "§8 ■ §7Gain §a+200 §6⸕ Mining Speed§7", "§8 ■ §7Gain §a+10 §b❄ Cold Resistance§7", "§8 ■ §7Reduce Pickaxe Ability cooldown by §a25%§7."]}, "surveyor": {"name": "Surveyor", "x": 5, "y": 2, "maxLevel": 20, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "stat": "(* level 0.75)", "lore": ["§7Increases your chances of finding a", "§7§bGlacite Mineshaft §7when mining in the", "§7§bGlacite Tunnels §7by §a§a{stat}%§7."]}, "metal_head": {"name": "Metal Head", "x": 1, "y": 1, "maxLevel": 20, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 4)", "stat": "(* level 5)", "lore": ["§7Grants §6+{stat}☘ Dwarven Metal Fortune§7."]}, "rags_to_riches": {"name": "Rags to <PERSON><PERSON>", "x": 3, "y": 1, "maxLevel": 50, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.05)", "stat": "(* level 4)", "lore": ["§7Grants §a+§a{stat} §6☘ Mining Fortune §7while", "§7inside a §bGlacite Mineshaft§7."]}, "eager_adventurer": {"name": "Eager Adventurer", "x": 5, "y": 1, "maxLevel": 100, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.3)", "stat": "(* level 4)", "lore": ["§7Grants §a+§a{stat}§7 §6⸕ Mining Speed §7while", "§7inside the §bGlacite Mineshafts§7."]}, "gemstone_infusion": {"name": "Gemstone Infusion", "x": 0, "y": 0, "maxLevel": 1, "powder": "GLACITE", "item": "(api level0)", "cost": "0", "statDuration": "(if (lt potm 2) \"20\" \"25\")", "lore": ["", "§6Pickaxe Ability: Gemstone Infusion", "§7Increases the effectiveness of", "§7§6<PERSON><PERSON> G<PERSON> §7in your pick's", "§7Gemstone Slots by §a100% §7for §a{statDuration}s§7.", "§8Cooldown: §a140s"]}, "crystalline": {"name": "Crystalline", "x": 1, "y": 0, "maxLevel": 50, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.3)", "stat": "(* level 0.5)", "lore": ["§7Increases your chances of finding a", "§7§bGlacite Mineshaft§7 containing a", "§7§dGemstone Crystal§7 by §a§a{stat}%§7."]}, "gifts_from_the_departed": {"name": "Gifts from the Departed", "x": 2, "y": 0, "maxLevel": 100, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 2.45)", "stat": "(* level 0.2)", "lore": ["§7Gain a §a§a{stat}% §7chance to get an extra", "§7item when looting a §bFrozen Corpse§7."]}, "mining_master": {"name": "Mining Master", "x": 3, "y": 0, "maxLevel": 10, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 8) 5)", "stat": "(* level 0.1)", "lore": ["§7Grants §5+{stat}✧ Pristine§7."]}, "hungry_for_more": {"name": "Dead Man's Chest", "x": 4, "y": 0, "maxLevel": 50, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.2)", "stat": "(* level 1)", "lore": ["§7Gain a §a§a{stat}% §7chance to spawn §a1", "§a§7additional §bFrozen Corpse §7when you", "§7enter a §bGlacite Mineshaft§7."]}, "vanguard_seeker": {"name": "Vanguard Seeker", "x": 5, "y": 0, "maxLevel": 50, "powder": "GLACITE", "item": "(npi level0 maxLevel)", "cost": "(pow (+ level 2) 3.1)", "stat": "(* level 1)", "lore": ["§7Increases your chances of finding a", "§7§bGlacite Mineshaft§7 containing a", "§7§fVanguard Corpse§7 by §a§a{stat}%§7."]}, "sheer_force": {"name": "Sheer Force", "x": 6, "y": 0, "maxLevel": 1, "powder": "GLACITE", "item": "(api level0)", "cost": "0", "stat": "(if (lt potm 2) \"20\" \"25\")", "lore": ["", "§6Pickaxe Ability: Sheer Force", "§7Grants §e+200▚ Mining Spread§7 for §a§a25s§7.", "§8Cooldown: §a120s"]}}}}