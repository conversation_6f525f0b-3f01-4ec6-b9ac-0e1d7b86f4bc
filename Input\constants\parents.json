{"ADAPTIVE_BOOTS": ["STARRED_ADAPTIVE_BOOTS"], "ADAPTIVE_CHESTPLATE": ["STARRED_ADAPTIVE_CHESTPLATE"], "ADAPTIVE_HELMET": ["STARRED_ADAPTIVE_HELMET"], "ADAPTIVE_LEGGINGS": ["STARRED_ADAPTIVE_LEGGINGS"], "AIMING;1": ["AIMING;2", "AIMING;3", "AIMING;4", "AIMING;5"], "ANGLER;1": ["ANGLER;2", "ANGLER;3", "ANGLER;4", "ANGLER;5", "ANGLER;6"], "BANE_OF_ARTHROPODS;1": ["BANE_OF_ARTHROPODS;2", "BANE_OF_ARTHROPODS;3", "BANE_OF_ARTHROPODS;4", "BANE_OF_ARTHROPODS;5", "BANE_OF_ARTHROPODS;6", "BANE_OF_ARTHROPODS;7"], "BANNER": ["BANNER-1", "BANNER-2", "BANNER-3", "BANNER-4", "BANNER-5", "BANNER-6", "BANNER-7", "BANNER-8", "BANNER-9", "BANNER-10", "BANNER-11", "BANNER-12", "BANNER-13", "BANNER-14", "BANNER-15"], "BIG_BRAIN;3": ["BIG_BRAIN;4", "BIG_BRAIN;5"], "BLAST_PROTECTION;1": ["BLAST_PROTECTION;2", "BLAST_PROTECTION;3", "BLAST_PROTECTION;4", "BLAST_PROTECTION;5", "BLAST_PROTECTION;6", "BLAST_PROTECTION;7"], "BLESSING;1": ["BLESSING;2", "BLESSING;3", "BLESSING;4", "BLESSING;5", "BLESSING;6"], "BONZO_BOSS": ["SCARF_BOSS", "PROFESSOR_BOSS", "THORN_BOSS", "LIVID_BOSS", "SADAN_BOSS", "NECRON_BOSS"], "BONZO_MASK": ["STARRED_BONZO_MASK"], "BONZO_STAFF": ["STARRED_BONZO_STAFF"], "CARPET": ["CARPET-1", "CARPET-2", "CARPET-3", "CARPET-4", "CARPET-5", "CARPET-6", "CARPET-7", "CARPET-8", "CARPET-9", "CARPET-10", "CARPET-11", "CARPET-12", "CARPET-13", "CARPET-14", "CARPET-15"], "CASTER;1": ["CASTER;2", "CASTER;3", "CASTER;4", "CASTER;5", "CASTER;6"], "CLEAVE;1": ["CLEAVE;2", "CLEAVE;3", "CLEAVE;4", "CLEAVE;5", "CLEAVE;6"], "CHANCE;1": ["CHANCE;2", "CHANCE;3", "CHANCE;4", "CHANCE;5"], "CHICKEN_AXE": ["COW_AXE", "MUSHROOM_COW_AXE", "PIG_AXE", "RABBIT_AXE", "SHEEP_AXE"], "COAL": ["COAL-1"], "COBBLE_WALL": ["COBBLE_WALL-1"], "CRITICAL;1": ["CRITICAL;2", "CRITICAL;3", "CRITICAL;4", "CRITICAL;5", "CRITICAL;6", "CRITICAL;7"], "CUBISM;1": ["CUBISM;2", "CUBISM;3", "CUBISM;4", "CUBISM;5", "CUBISM;6"], "DEPTH_STRIDER;1": ["DEPTH_STRIDER;2", "DEPTH_STRIDER;3"], "DIRT": ["DIRT-1"], "DRAGON_HUNTER;1": ["DRAGON_HUNTER;2", "DRAGON_HUNTER;3", "DRAGON_HUNTER;4", "DRAGON_HUNTER;5"], "EFFICIENCY;1": ["EFFICIENCY;2", "EFFICIENCY;3", "EFFICIENCY;4", "EFFICIENCY;5", "EFFICIENCY;6"], "ENDER_SLAYER;1": ["ENDER_SLAYER;2", "ENDER_SLAYER;3", "ENDER_SLAYER;4", "ENDER_SLAYER;5", "ENDER_SLAYER;6", "ENDER_SLAYER;7"], "EXECUTE;1": ["EXECUTE;2", "EXECUTE;3", "EXECUTE;4", "EXECUTE;5", "EXECUTE;6"], "EXP_BOTTLE": ["GRAND_EXP_BOTTLE", "TITANIC_EXP_BOTTLE", "COLOSSAL_EXP_BOTTLE"], "EXPERIENCE;1": ["EXPERIENCE;2", "EXPERIENCE;3", "EXPERIENCE;4", "EXPERIENCE;5"], "FEATHER_FALLING;1": ["FEATHER_FALLING;2", "FEATHER_FALLING;3", "FEATHER_FALLING;4", "FEATHER_FALLING;5", "FEATHER_FALLING;6", "FEATHER_FALLING;7", "FEATHER_FALLING;8", "FEATHER_FALLING;9", "FEATHER_FALLING;10"], "FIRE_ASPECT;1": ["FIRE_ASPECT;2", "FIRE_ASPECT;3"], "FIRE_PROTECTION;1": ["FIRE_PROTECTION;2", "FIRE_PROTECTION;3", "FIRE_PROTECTION;4", "FIRE_PROTECTION;5", "FIRE_PROTECTION;6", "FIRE_PROTECTION;7"], "FIRST_STRIKE;1": ["FIRST_STRIKE;2", "FIRST_STRIKE;3", "FIRST_STRIKE;4", "FIRST_STRIKE;5"], "FORTUNE;1": ["FORTUNE;2", "FORTUNE;3", "FORTUNE;4"], "FRAIL;1": ["FRAIL;2", "FRAIL;3", "FRAIL;4", "FRAIL;5", "FRAIL;6"], "FROST_WALKER;1": ["FROST_WALKER;2"], "GIANT_KILLER;1": ["GIANT_KILLER;2", "GIANT_KILLER;3", "GIANT_KILLER;4", "GIANT_KILLER;5", "GIANT_KILLER;6", "GIANT_KILLER;7"], "GOLDEN_APPLE": ["GOLDEN_APPLE-1"], "GROWTH;1": ["GROWTH;2", "GROWTH;3", "GROWTH;4", "GROWTH;5", "GROWTH;6", "GROWTH;7"], "HARVESTING;1": ["HARVESTING;2", "HARVESTING;3", "HARVESTING;4", "HARVESTING;5", "HARVESTING;6"], "IMPALING;1": ["IMPALING;2", "IMPALING;3"], "INFINITE_QUIVER;1": ["INFINITE_QUIVER;2", "INFINITE_QUIVER;3", "INFINITE_QUIVER;4", "INFINITE_QUIVER;5", "INFINITE_QUIVER;6", "INFINITE_QUIVER;7", "INFINITE_QUIVER;8", "INFINITE_QUIVER;9", "INFINITE_QUIVER;10"], "INK_SACK": ["INK_SACK-1", "INK_SACK-2", "INK_SACK-3", "INK_SACK-4", "INK_SACK-5", "INK_SACK-6", "INK_SACK-7", "INK_SACK-8", "INK_SACK-9", "INK_SACK-10", "INK_SACK-11", "INK_SACK-12", "INK_SACK-13", "INK_SACK-14", "INK_SACK-15"], "KNOCKBACK;1": ["KNOCKBACK;2"], "LAST_BREATH": ["STARRED_LAST_BREATH"], "LETHALITY;1": ["LETHALITY;2", "LETHALITY;3", "LETHALITY;4", "LETHALITY;5", "LETHALITY;6"], "LIFE_STEAL;1": ["LIFE_STEAL;2", "LIFE_STEAL;3", "LIFE_STEAL;4", "LIFE_STEAL;5"], "LOG": ["LOG-1", "LOG-2", "LOG-3", "LOG_2", "LOG_2-1"], "LOOTING;1": ["LOOTING;2", "LOOTING;3", "LOOTING;4", "LOOTING;5"], "LUCK;1": ["LUCK;2", "LUCK;3", "LUCK;4", "LUCK;5", "LUCK;6", "LUCK;7"], "LUCK_OF_THE_SEA;1": ["LUCK_OF_THE_SEA;2", "LUCK_OF_THE_SEA;3", "LUCK_OF_THE_SEA;4", "LUCK_OF_THE_SEA;5", "LUCK_OF_THE_SEA;6"], "LURE;1": ["LURE;2", "LURE;3", "LURE;4", "LURE;5", "LURE;6"], "MAGNET;1": ["MAGNET;2", "MAGNET;3", "MAGNET;4", "MAGNET;5", "MAGNET;6"], "NECRON_BLADE": ["VALKYRIE", "SCYLLA", "ASTRAEA", "HYPERION"], "OVERLOAD;1": ["OVERLOAD;2", "OVERLOAD;3", "OVERLOAD;4", "OVERLOAD;5"], "PERFECT_BOOTS_1": ["PERFECT_BOOTS_2", "PERFECT_BOOTS_3", "PERFECT_BOOTS_4", "PERFECT_BOOTS_5", "PERFECT_BOOTS_6", "PERFECT_BOOTS_7", "PERFECT_BOOTS_8", "PERFECT_BOOTS_9", "PERFECT_BOOTS_10", "PERFECT_BOOTS_11", "PERFECT_BOOTS_12", "PERFECT_BOOTS_13"], "PERFECT_CHESTPLATE_1": ["PERFECT_CHESTPLATE_2", "PERFECT_CHESTPLATE_3", "PERFECT_CHESTPLATE_4", "PERFECT_CHESTPLATE_5", "PERFECT_CHESTPLATE_6", "PERFECT_CHESTPLATE_7", "PERFECT_CHESTPLATE_8", "PERFECT_CHESTPLATE_9", "PERFECT_CHESTPLATE_10", "PERFECT_CHESTPLATE_11", "PERFECT_CHESTPLATE_12", "PERFECT_CHESTPLATE_13"], "PERFECT_HELMET_1": ["PERFECT_HELMET_2", "PERFECT_HELMET_3", "PERFECT_HELMET_4", "PERFECT_HELMET_5", "PERFECT_HELMET_6", "PERFECT_HELMET_7", "PERFECT_HELMET_8", "PERFECT_HELMET_9", "PERFECT_HELMET_10", "PERFECT_HELMET_11", "PERFECT_HELMET_12", "PERFECT_HELMET_13"], "PERFECT_LEGGINGS_1": ["PERFECT_LEGGINGS_2", "PERFECT_LEGGINGS_3", "PERFECT_LEGGINGS_4", "PERFECT_LEGGINGS_5", "PERFECT_LEGGINGS_6", "PERFECT_LEGGINGS_7", "PERFECT_LEGGINGS_8", "PERFECT_LEGGINGS_9", "PERFECT_LEGGINGS_10", "PERFECT_LEGGINGS_11", "PERFECT_LEGGINGS_12", "PERFECT_LEGGINGS_13"], "POWER;1": ["POWER;2", "POWER;3", "POWER;4", "POWER;5", "POWER;6", "POWER;7"], "PRISMARINE": ["PRISMARINE-1", "PRISMARINE-2"], "PROJECTILE_PROTECTION;1": ["PROJECTILE_PROTECTION;2", "PROJECTILE_PROTECTION;3", "PROJECTILE_PROTECTION;4", "PROJECTILE_PROTECTION;5", "PROJECTILE_PROTECTION;6", "PROJECTILE_PROTECTION;7"], "PROTECTION;1": ["PROTECTION;2", "PROTECTION;3", "PROTECTION;4", "PROTECTION;5", "PROTECTION;6", "PROTECTION;7"], "PROSECUTE;1": ["PROSECUTE;2", "PROSECUTE;3", "PROSECUTE;4", "PROSECUTE;5", "PROSECUTE;6"], "PUNCH;1": ["PUNCH;2"], "QUARTZ_BLOCK": ["QUARTZ_BLOCK-1", "QUARTZ_BLOCK-2"], "RED_SANDSTONE": ["RED_SANDSTONE-1", "RED_SANDSTONE-2"], "REJUVENATE;1": ["REJUVENATE;2", "REJUVENATE;3", "REJUVENATE;4", "REJUVENATE;5"], "RESPIRATION;1": ["RESPIRATION;2", "RESPIRATION;3"], "RESPITE;1": ["RESPITE;2", "RESPITE;3", "RESPITE;4", "RESPITE;5"], "SAND": ["SAND-1"], "SANDSTONE": ["SANDSTONE-1", "SANDSTONE-2"], "SCAVENGER;1": ["SCAVENGER;2", "SCAVENGER;3", "SCAVENGER;4", "SCAVENGER;5"], "SHADOW_ASSASSIN_BOOTS": ["STARRED_SHADOW_ASSASSIN_BOOTS"], "SHADOW_ASSASSIN_CHESTPLATE": ["STARRED_SHADOW_ASSASSIN_CHESTPLATE"], "SHADOW_ASSASSIN_HELMET": ["STARRED_SHADOW_ASSASSIN_HELMET"], "SHADOW_ASSASSIN_LEGGINGS": ["STARRED_SHADOW_ASSASSIN_LEGGINGS"], "SHADOW_FURY": ["STARRED_SHADOW_FURY"], "SALMON_HELMET_NEW": ["SALMON_HELMET"], "SALMON_CHESTPLATE_NEW": ["SALMON_CHESTPLATE"], "SALMON_LEGGINGS_NEW": ["SALMON_LEGGINGS"], "SALMON_BOOTS_NEW": ["SALMON_BOOTS"], "SHARPNESS;1": ["SHARPNESS;2", "SHARPNESS;3", "SHARPNESS;4", "SHARPNESS;5", "SHARPNESS;6", "SHARPNESS;7"], "SMITE;1": ["SMITE;2", "SMITE;3", "SMITE;4", "SMITE;5", "SMITE;6", "SMITE;7"], "SMOOTH_BRICK": ["SMOOTH_BRICK-1", "SMOOTH_BRICK-3"], "SNIPE;1": ["SNIPE;2", "SNIPE;3", "SNIPE;4"], "SYPHON;1": ["SYPHON;2", "SYPHON;3", "SYPHON;4", "SYPHON;5"], "SPIDER_QUEENS_STINGER": ["STARRED_SPIDER_QUEENS_STINGER"], "SPIKED_HOOK;1": ["SPIKED_HOOK;2", "SPIKED_HOOK;3", "SPIKED_HOOK;4", "SPIKED_HOOK;5", "SPIKED_HOOK;6"], "STAINED_CLAY": ["STAINED_CLAY-1", "STAINED_CLAY-2", "STAINED_CLAY-3", "STAINED_CLAY-4", "STAINED_CLAY-5", "STAINED_CLAY-6", "STAINED_CLAY-7", "STAINED_CLAY-8", "STAINED_CLAY-9", "STAINED_CLAY-10", "STAINED_CLAY-11", "STAINED_CLAY-12", "STAINED_CLAY-13", "STAINED_CLAY-14", "STAINED_CLAY-15"], "STAINED_GLASS": ["STAINED_GLASS-1", "STAINED_GLASS-2", "STAINED_GLASS-3", "STAINED_GLASS-4", "STAINED_GLASS-5", "STAINED_GLASS-6", "STAINED_GLASS-7", "STAINED_GLASS-8", "STAINED_GLASS-9", "STAINED_GLASS-10", "STAINED_GLASS-11", "STAINED_GLASS-12", "STAINED_GLASS-13", "STAINED_GLASS-14", "STAINED_GLASS-15"], "STAINED_GLASS_PANE": ["STAINED_GLASS_PANE-1", "STAINED_GLASS_PANE-2", "STAINED_GLASS_PANE-3", "STAINED_GLASS_PANE-4", "STAINED_GLASS_PANE-5", "STAINED_GLASS_PANE-6", "STAINED_GLASS_PANE-7", "STAINED_GLASS_PANE-8", "STAINED_GLASS_PANE-9", "STAINED_GLASS_PANE-10", "STAINED_GLASS_PANE-11", "STAINED_GLASS_PANE-12", "STAINED_GLASS_PANE-13", "STAINED_GLASS_PANE-14", "STAINED_GLASS_PANE-15"], "STEP": ["STEP-1", "STEP-3", "STEP-4", "STEP-5", "STEP-6", "STEP-7"], "STONE": ["STONE-1", "STONE-2", "STONE-3", "STONE-4", "STONE-5", "STONE-6"], "SUPER_COMPACTOR_3000": ["DWARVEN_COMPACTOR"], "STONE_BLADE": ["STARRED_STONE_BLADE"], "SUGAR_RUSH;1": ["SUGAR_RUSH;2", "SUGAR_RUSH;3"], "THORNS;1": ["THORNS;2", "THORNS;3"], "THEORETICAL_HOE_WHEAT_3": ["THEORETICAL_HOE_WHEAT_2", "THEORETICAL_HOE_WHEAT_1"], "THEORETICAL_HOE_WARTS_3": ["THEORETICAL_HOE_WARTS_2", "THEORETICAL_HOE_WARTS_1"], "THEORETICAL_HOE_POTATO_3": ["THEORETICAL_HOE_POTATO_2", "THEORETICAL_HOE_POTATO_1"], "THEORETICAL_HOE_CARROT_3": ["THEORETICAL_HOE_CARROT_2", "THEORETICAL_HOE_CARROT_1"], "THEORETICAL_HOE_CANE_3": ["THEORETICAL_HOE_CANE_2", "THEORETICAL_HOE_CANE_1"], "THUNDERLORD;1": ["THUNDERLORD;2", "THUNDERLORD;3", "THUNDERLORD;4", "THUNDERLORD;5", "THUNDERLORD;6", "THUNDERLORD;7"], "THUNDERBOLT;1": ["THUNDERBOLT;2", "THUNDERBOLT;3", "THUNDERBOLT;4", "THUNDERBOLT;5", "THUNDERBOLT;6", "THUNDERBOLT;7"], "TITAN_KILLER;1": ["TITAN_KILLER;2", "TITAN_KILLER;3", "TITAN_KILLER;4", "TITAN_KILLER;5", "TITAN_KILLER;6", "TITAN_KILLER;7"], "TRIPLE_STRIKE;1": ["TRIPLE_STRIKE;2", "TRIPLE_STRIKE;3", "TRIPLE_STRIKE;4", "TRIPLE_STRIKE;5"], "TURBO_CACTUS;1": ["TURBO_CACTUS;2", "TURBO_CACTUS;3", "TURBO_CACTUS;4", "TURBO_CACTUS;5"], "TURBO_CANE;1": ["TURBO_CANE;2", "TURBO_CANE;3", "TURBO_CANE;4", "TURBO_CANE;5"], "TURBO_CARROT;1": ["TURBO_CARROT;2", "TURBO_CARROT;3", "TURBO_CARROT;4", "TURBO_CARROT;5"], "TURBO_COCO;1": ["TURBO_COCO;2", "TURBO_COCO;3", "TURBO_COCO;4", "TURBO_COCO;5"], "TURBO_MELON;1": ["TURBO_MELON;2", "TURBO_MELON;3", "TURBO_MELON;4", "TURBO_MELON;5"], "TURBO_MUSHROOMS;1": ["TURBO_MUSHROOMS;2", "TURBO_MUSHROOMS;3", "TURBO_MUSHROOMS;4", "TURBO_MUSHROOMS;5"], "TURBO_POTATO;1": ["TURBO_POTATO;2", "TURBO_POTATO;3", "TURBO_POTATO;4", "TURBO_POTATO;5"], "TURBO_PUMPKIN;1": ["TURBO_PUMPKIN;2", "TURBO_PUMPKIN;3", "TURBO_PUMPKIN;4", "TURBO_PUMPKIN;5"], "TURBO_WARTS;1": ["TURBO_WARTS;2", "TURBO_WARTS;3", "TURBO_WARTS;4", "TURBO_WARTS;5"], "TURBO_WHEAT;1": ["TURBO_WHEAT;2", "TURBO_WHEAT;3", "TURBO_WHEAT;4", "TURBO_WHEAT;5"], "ULTIMATE_BANK;1": ["ULTIMATE_BANK;2", "ULTIMATE_BANK;3", "ULTIMATE_BANK;4", "ULTIMATE_BANK;5"], "ULTIMATE_CHIMERA;1": ["ULTIMATE_CHIMERA;2", "ULTIMATE_CHIMERA;3", "ULTIMATE_CHIMERA;4", "ULTIMATE_CHIMERA;5"], "ULTIMATE_COMBO;1": ["ULTIMATE_COMBO;2", "ULTIMATE_COMBO;3", "ULTIMATE_COMBO;4", "ULTIMATE_COMBO;5"], "ULTIMATE_JERRY;1": ["ULTIMATE_JERRY;2", "ULTIMATE_JERRY;3", "ULTIMATE_JERRY;4", "ULTIMATE_JERRY;5"], "ULTIMATE_LAST_STAND;1": ["ULTIMATE_LAST_STAND;2", "ULTIMATE_LAST_STAND;3", "ULTIMATE_LAST_STAND;4", "ULTIMATE_LAST_STAND;5"], "ULTIMATE_LEGION;1": ["ULTIMATE_LEGION;2", "ULTIMATE_LEGION;3", "ULTIMATE_LEGION;4", "ULTIMATE_LEGION;5"], "ULTIMATE_NO_PAIN_NO_GAIN;1": ["ULTIMATE_NO_PAIN_NO_GAIN;2", "ULTIMATE_NO_PAIN_NO_GAIN;3", "ULTIMATE_NO_PAIN_NO_GAIN;4", "ULTIMATE_NO_PAIN_NO_GAIN;5"], "ULTIMATE_REND;1": ["ULTIMATE_REND;2", "ULTIMATE_REND;3", "ULTIMATE_REND;4", "ULTIMATE_REND;5"], "ULTIMATE_SOUL_EATER;1": ["ULTIMATE_SOUL_EATER;2", "ULTIMATE_SOUL_EATER;3", "ULTIMATE_SOUL_EATER;4", "ULTIMATE_SOUL_EATER;5"], "ULTIMATE_SWARM;1": ["ULTIMATE_SWARM;2", "ULTIMATE_SWARM;3", "ULTIMATE_SWARM;4", "ULTIMATE_SWARM;5"], "ULTIMATE_WISDOM;1": ["ULTIMATE_WISDOM;2", "ULTIMATE_WISDOM;3", "ULTIMATE_WISDOM;4", "ULTIMATE_WISDOM;5"], "ULTIMATE_WISE;1": ["ULTIMATE_WISE;2", "ULTIMATE_WISE;3", "ULTIMATE_WISE;4", "ULTIMATE_WISE;5"], "ULTIMATE_REITERATE;1": ["ULTIMATE_REITERATE;2", "ULTIMATE_REITERATE;3", "ULTIMATE_REITERATE;4", "ULTIMATE_REITERATE;5"], "VAMPIRISM;1": ["VAMPIRISM;2", "VAMPIRISM;3", "VAMPIRISM;4", "VAMPIRISM;5", "VAMPIRISM;6"], "VENOMOUS;1": ["VENOMOUS;2", "VENOMOUS;3", "VENOMOUS;4", "VENOMOUS;5", "VENOMOUS;6"], "VICIOUS;3": ["VICIOUS;4", "VICIOUS;5"], "WEDDING_RING_9": ["WEDDING_RING_8", "WEDDING_RING_7", "WEDDING_RING_6", "WEDDING_RING_5", "WEDDING_RING_4", "WEDDING_RING_3", "WEDDING_RING_2", "WEDDING_RING_1", "WEDDING_RING_0"], "WITHER_BOOTS": ["POWER_WITHER_BOOTS", "SPEED_WITHER_BOOTS", "TANK_WITHER_BOOTS", "WISE_WITHER_BOOTS"], "WITHER_CHESTPLATE": ["POWER_WITHER_CHESTPLATE", "SPEED_WITHER_CHESTPLATE", "TANK_WITHER_CHESTPLATE", "WISE_WITHER_CHESTPLATE"], "WITHER_HELMET": ["POWER_WITHER_HELMET", "SPEED_WITHER_HELMET", "TANK_WITHER_HELMET", "WISE_WITHER_HELMET"], "WITHER_LEGGINGS": ["POWER_WITHER_LEGGINGS", "SPEED_WITHER_LEGGINGS", "TANK_WITHER_LEGGINGS", "WISE_WITHER_LEGGINGS"], "WOOD": ["WOOD-1", "WOOD-2", "WOOD-3", "WOOD-4", "WOOD-5"], "WOOD_STEP": ["WOOD_STEP-1", "WOOD_STEP-2", "WOOD_STEP-3", "WOOD_STEP-4", "WOOD_STEP-5"], "WOOL": ["WOOL-1", "WOOL-2", "WOOL-3", "WOOL-4", "WOOL-5", "WOOL-6", "WOOL-7", "WOOL-8", "WOOL-9", "WOOL-10", "WOOL-11", "WOOL-12", "WOOL-13", "WOOL-14", "WOOL-15"], "BEE;4": ["BEE;3", "BEE;2", "BEE;1", "BEE;0"], "CHICKEN;4": ["CHICKEN;3", "CHICKEN;2", "CHICKEN;1", "CHICKEN;0"], "ELEPHANT;4": ["ELEPHANT;3", "ELEPHANT;2", "ELEPHANT;1", "ELEPHANT;0"], "FRACTURED_MONTEZUMA_SOUL;3": ["FRACTURED_MONTEZUMA_SOUL;2"], "PIG;4": ["PIG;3", "PIG;2", "PIG;1", "PIG;0"], "RABBIT;5": ["RABBIT;4", "RABBIT;3", "RABBIT;2", "RABBIT;1", "RABBIT;0"], "GIRAFFE;4": ["GIRAFFE;3", "GIRAFFE;2", "GIRAFFE;1", "GIRAFFE;0"], "LION;4": ["LION;3", "LION;2", "LION;1", "LION;0"], "MONKEY;4": ["MONKEY;3", "MONKEY;2", "MONKEY;1", "MONKEY;0"], "OCELOT;4": ["OCELOT;3", "OCELOT;2", "OCELOT;1", "OCELOT;0"], "GUARDIAN;5": ["GUARDIAN;4", "GUARDIAN;3", "GUARDIAN;2", "GUARDIAN;1", "GUARDIAN;0"], "BAT;5": ["BAT;4", "BAT;3", "BAT;2", "BAT;1", "BAT;0"], "ENDERMITE;5": ["ENDERMITE;4", "ENDERMITE;3", "ENDERMITE;2", "ENDERMITE;1", "ENDERMITE;0"], "ROCK;4": ["ROCK;3", "ROCK;2", "ROCK;1", "ROCK;0"], "ARMADILLO;4": ["ARMADILLO;3", "ARMADILLO;2", "ARMADILLO;1", "ARMADILLO;0"], "SILVERFISH;4": ["SILVERFISH;3", "SILVERFISH;2", "SILVERFISH;1", "SILVERFISH;0"], "BLUE_WHALE;4": ["BLUE_WHALE;3", "BLUE_WHALE;2", "BLUE_WHALE;1", "BLUE_WHALE;0"], "DOLPHIN;4": ["DOLPHIN;3", "DOLPHIN;2", "DOLPHIN;1", "DOLPHIN;0"], "SQUID;4": ["SQUID;3", "SQUID;2", "SQUID;1", "SQUID;0"], "SHEEP;4": ["SHEEP;3", "SHEEP;2", "SHEEP;1", "SHEEP;0"], "ENDERMAN;5": ["ENDERMAN;4", "ENDERMAN;3", "ENDERMAN;2", "ENDERMAN;1", "ENDERMAN;0"], "GRIFFIN;4": ["GRIFFIN;3", "GRIFFIN;2", "GRIFFIN;1", "GRIFFIN;0"], "HORSE;4": ["HORSE;3", "HORSE;2", "HORSE;1", "HORSE;0"], "JERRY;5": ["JERRY;4", "JERRY;3", "JERRY;2", "JERRY;1", "JERRY;0"], "MAGMA_CUBE;4": ["MAGMA_CUBE;3", "MAGMA_CUBE;2", "MAGMA_CUBE;1", "MAGMA_CUBE;0"], "SKELETON;4": ["SKELETON;3", "SKELETON;2", "SKELETON;1", "SKELETON;0"], "SPIDER;5": ["SPIDER;4", "SPIDER;3", "SPIDER;2", "SPIDER;1", "SPIDER;0"], "TIGER;4": ["TIGER;3", "TIGER;2", "TIGER;1", "TIGER;0"], "WOLF;4": ["WOLF;3", "WOLF;2", "WOLF;1", "WOLF;0"], "ZOMBIE;4": ["ZOMBIE;3", "ZOMBIE;2", "ZOMBIE;1", "ZOMBIE;0"], "ENDER_DRAGON;4": ["ENDER_DRAGON;3"], "WITHER_SKELETON;4": ["WITHER_SKELETON;3"], "BLAZE;4": ["BLAZE;3"], "GHOUL;4": ["GHOUL;3"], "GOLEM;4": ["GOLEM;3"], "HOUND;4": ["HOUND;3"], "PHOENIX;4": ["PHOENIX;3"], "PIGMAN;4": ["PIGMAN;3"], "SPIRIT;4": ["SPIRIT;3"], "TARANTULA;5": ["TARANTULA;4", "TARANTULA;3"], "TURTLE;4": ["TURTLE;3"], "BABY_YETI;4": ["BABY_YETI;3"], "MEGALODON;4": ["MEGALODON;3"], "PARROT;4": ["PARROT;3"], "JELLYFISH;4": ["JELLYFISH;3"], "FLYING_FISH;5": ["FLYING_FISH;4", "FLYING_FISH;3", "FLYING_FISH;2"], "GRIFFIN_UPGRADE_STONE_LEGENDARY": ["GRIFFIN_UPGRADE_STONE_EPIC", "GRIFFIN_UPGRADE_STONE_RARE", "GRIFFIN_UPGRADE_STONE_UNCOMMON", "BASE_GRIFFIN_UPGRADE_STONE"], "SPEED_ARTIFACT": ["SPEED_RING", "SPEED_TALISMAN"], "FEATHER_ARTIFACT": ["FEATHER_RING", "FEATHER_TALISMAN"], "ARTIFACT_POTION_AFFINITY": ["RING_POTION_AFFINITY", "POTION_AFFINITY_TALISMAN"], "SEA_CREATURE_ARTIFACT": ["SEA_CREATURE_RING", "SEA_CREATURE_TALISMAN"], "ZOMBIE_ARTIFACT": ["ZOMBIE_RING", "ZOMBIE_TALISMAN"], "INTIMIDATION_RELIC": ["INTIMIDATION_ARTIFACT", "INTIMIDATION_RING", "INTIMIDATION_TALISMAN"], "BAT_ARTIFACT": ["BAT_RING", "BAT_TALISMAN"], "SPIDER_ARTIFACT": ["SPIDER_RING", "SPIDER_TALISMAN"], "RED_CLAW_ARTIFACT": ["RED_CLAW_RING", "RED_CLAW_TALISMAN"], "PIGGY_BANK": ["CRACKED_PIGGY_BANK", "BROKEN_PIGGY_BANK"], "HEALING_RING": ["HEALING_TALISMAN"], "PERSONAL_COMPACTER_7000": ["PERSONAL_COMPACTER_6000", "PERSONAL_COMPACTER_5000", "PERSONAL_COMPACTER_4000"], "CANDY_RELIC": ["CANDY_ARTIFACT", "CANDY_RING", "CANDY_TALISMAN"], "BEASTMASTER_CREST_LEGENDARY": ["BEASTMASTER_CREST_EPIC", "BEASTMASTER_CREST_RARE", "BEASTMASTER_CREST_UNCOMMON", "BEASTMASTER_CREST_COMMON"], "RAZOR_SHARP_SHARK_TOOTH_NECKLACE": ["SHARP_SHARK_TOOTH_NECKLACE", "HONED_SHARK_TOOTH_NECKLACE", "DULL_SHARK_TOOTH_NECKLACE", "RAGGEDY_SHARK_TOOTH_NECKLACE"], "WOLF_RING": ["WOLF_TALISMAN"], "SEAL_OF_THE_FAMILY": ["CROOKED_ARTIFACT", "SHADY_RING"], "TREASURE_ARTIFACT": ["TREASURE_RING", "TREASURE_TALISMAN"], "SCARF_GRIMOIRE": ["SCARF_THESIS", "SCARF_STUDIES"], "WITHER_RELIC": ["WITHER_ARTIFACT"], "BAT_PERSON_ARTIFACT": ["BAT_PERSON_RING", "BAT_PERSON_TALISMAN"], "PERSONAL_COMPACTOR_7000": ["PERSONAL_COMPACTOR_6000", "PERSONAL_COMPACTOR_5000", "PERSONAL_COMPACTOR_4000"], "ETERNAL_HOOF": ["LUCKY_HOOF"], "CHEETAH_TALISMAN": ["LYNX_TALISMAN", "CAT_TALISMAN"], "TALISMAN_ENRICHMENT_CRITICAL_CHANCE": ["TALISMAN_ENRICHMENT_CRITICAL_DAMAGE", "TALISMAN_ENRICHMENT_DEFENSE", "TALISMAN_ENRICHMENT_HEALTH", "TALISMAN_ENRICHMENT_INTELLIGENCE", "TALISMAN_ENRICHMENT_MAGIC_FIND", "TALISMAN_ENRICHMENT_WALK_SPEED", "TALISMAN_ENRICHMENT_STRENGTH", "TALISMAN_ENRICHMENT_ATTACK_SPEED", "TALISMAN_ENRICHMENT_FEROCITY", "TALISMAN_ENRICHMENT_SEA_CREATURE_CHANCE"], "PET_ITEM_COMBAT_SKILL_BOOST_EPIC": ["PET_ITEM_COMBAT_SKILL_BOOST_RARE", "PET_ITEM_COMBAT_SKILL_BOOST_UNCOMMON", "PET_ITEM_COMBAT_SKILL_BOOST_COMMON"], "PET_ITEM_FISHING_SKILL_BOOST_EPIC": ["PET_ITEM_FISHING_SKILL_BOOST_RARE", "PET_ITEM_FISHING_SKILL_BOOST_UNCOMMON", "PET_ITEM_FISHING_SKILL_BOOST_COMMON"], "PET_ITEM_FARMING_SKILL_BOOST_EPIC": ["PET_ITEM_FARMING_SKILL_BOOST_RARE", "PET_ITEM_FARMING_SKILL_BOOST_UNCOMMON", "PET_ITEM_FARMING_SKILL_BOOST_COMMON"], "PET_ITEM_FORAGING_SKILL_BOOST_EPIC": ["PET_ITEM_FORAGING_SKILL_BOOST_COMMON"], "PET_ITEM_MINING_SKILL_BOOST_RARE": ["PET_ITEM_MINING_SKILL_BOOST_UNCOMMON", "PET_ITEM_MINING_SKILL_BOOST_COMMON"], "ALL_SKILLS_SUPER_BOOST": ["PET_ITEM_ALL_SKILLS_BOOST_COMMON"], "JERRY_BOX_GOLDEN": ["JERRY_BOX_PURPLE", "JERRY_BOX_BLUE", "JERRY_BOX_GREEN"], "JERRY_TALISMAN_GOLDEN": ["JERRY_TALISMAN_PURPLE", "JERRY_TALISMAN_BLUE", "JERRY_TALISMAN_GREEN"], "COBBLESTONE_GENERATOR_1": ["COBBLESTONE_GENERATOR_2", "COBBLESTONE_GENERATOR_3", "COBBLESTONE_GENERATOR_4", "COBBLESTONE_GENERATOR_5", "COBBLESTONE_GENERATOR_6", "COBBLESTONE_GENERATOR_7", "COBBLESTONE_GENERATOR_8", "COBBLESTONE_GENERATOR_9", "COBBLESTONE_GENERATOR_10", "COBBLESTONE_GENERATOR_11", "COBBLESTONE_GENERATOR_12"], "OBSIDIAN_GENERATOR_1": ["OBSIDIAN_GENERATOR_2", "OBSIDIAN_GENERATOR_3", "OBSIDIAN_GENERATOR_4", "OBSIDIAN_GENERATOR_5", "OBSIDIAN_GENERATOR_6", "OBSIDIAN_GENERATOR_7", "OBSIDIAN_GENERATOR_8", "OBSIDIAN_GENERATOR_9", "OBSIDIAN_GENERATOR_10", "OBSIDIAN_GENERATOR_11", "OBSIDIAN_GENERATOR_12"], "GLOWSTONE_GENERATOR_1": ["GLOWSTONE_GENERATOR_2", "GLOWSTONE_GENERATOR_3", "GLOWSTONE_GENERATOR_4", "GLOWSTONE_GENERATOR_5", "GLOWSTONE_GENERATOR_6", "GLOWSTONE_GENERATOR_7", "GLOWSTONE_GENERATOR_8", "GLOWSTONE_GENERATOR_9", "GLOWSTONE_GENERATOR_10", "GLOWSTONE_GENERATOR_11", "GLOWSTONE_GENERATOR_12"], "GRAVEL_GENERATOR_1": ["GRAVEL_GENERATOR_2", "GRAVEL_GENERATOR_3", "GRAVEL_GENERATOR_4", "GRAVEL_GENERATOR_5", "GRAVEL_GENERATOR_6", "GRAVEL_GENERATOR_7", "GRAVEL_GENERATOR_8", "GRAVEL_GENERATOR_9", "GRAVEL_GENERATOR_10", "GRAVEL_GENERATOR_11"], "SAND_GENERATOR_1": ["SAND_GENERATOR_2", "SAND_GENERATOR_3", "SAND_GENERATOR_4", "SAND_GENERATOR_5", "SAND_GENERATOR_6", "SAND_GENERATOR_7", "SAND_GENERATOR_8", "SAND_GENERATOR_9", "SAND_GENERATOR_10", "SAND_GENERATOR_11"], "CLAY_GENERATOR_1": ["CLAY_GENERATOR_2", "CLAY_GENERATOR_3", "CLAY_GENERATOR_4", "CLAY_GENERATOR_5", "CLAY_GENERATOR_6", "CLAY_GENERATOR_7", "CLAY_GENERATOR_8", "CLAY_GENERATOR_9", "CLAY_GENERATOR_10", "CLAY_GENERATOR_11"], "ICE_GENERATOR_1": ["ICE_GENERATOR_2", "ICE_GENERATOR_3", "ICE_GENERATOR_4", "ICE_GENERATOR_5", "ICE_GENERATOR_6", "ICE_GENERATOR_7", "ICE_GENERATOR_8", "ICE_GENERATOR_9", "ICE_GENERATOR_10", "ICE_GENERATOR_11", "ICE_GENERATOR_12"], "SNOW_GENERATOR_1": ["SNOW_GENERATOR_2", "SNOW_GENERATOR_3", "SNOW_GENERATOR_4", "SNOW_GENERATOR_5", "SNOW_GENERATOR_6", "SNOW_GENERATOR_7", "SNOW_GENERATOR_8", "SNOW_GENERATOR_9", "SNOW_GENERATOR_10", "SNOW_GENERATOR_11", "SNOW_GENERATOR_12"], "COAL_GENERATOR_1": ["COAL_GENERATOR_2", "COAL_GENERATOR_3", "COAL_GENERATOR_4", "COAL_GENERATOR_5", "COAL_GENERATOR_6", "COAL_GENERATOR_7", "COAL_GENERATOR_8", "COAL_GENERATOR_9", "COAL_GENERATOR_10", "COAL_GENERATOR_11", "COAL_GENERATOR_12"], "IRON_GENERATOR_1": ["IRON_GENERATOR_2", "IRON_GENERATOR_3", "IRON_GENERATOR_4", "IRON_GENERATOR_5", "IRON_GENERATOR_6", "IRON_GENERATOR_7", "IRON_GENERATOR_8", "IRON_GENERATOR_9", "IRON_GENERATOR_10", "IRON_GENERATOR_11", "IRON_GENERATOR_12"], "GOLD_GENERATOR_1": ["GOLD_GENERATOR_2", "GOLD_GENERATOR_3", "GOLD_GENERATOR_4", "GOLD_GENERATOR_5", "GOLD_GENERATOR_6", "GOLD_GENERATOR_7", "GOLD_GENERATOR_8", "GOLD_GENERATOR_9", "GOLD_GENERATOR_10", "GOLD_GENERATOR_11", "GOLD_GENERATOR_12"], "DIAMOND_GENERATOR_1": ["DIAMOND_GENERATOR_2", "DIAMOND_GENERATOR_3", "DIAMOND_GENERATOR_4", "DIAMOND_GENERATOR_5", "DIAMOND_GENERATOR_6", "DIAMOND_GENERATOR_7", "DIAMOND_GENERATOR_8", "DIAMOND_GENERATOR_9", "DIAMOND_GENERATOR_10", "DIAMOND_GENERATOR_11", "DIAMOND_GENERATOR_12"], "LAPIS_GENERATOR_1": ["LAPIS_GENERATOR_2", "LAPIS_GENERATOR_3", "LAPIS_GENERATOR_4", "LAPIS_GENERATOR_5", "LAPIS_GENERATOR_6", "LAPIS_GENERATOR_7", "LAPIS_GENERATOR_8", "LAPIS_GENERATOR_9", "LAPIS_GENERATOR_10", "LAPIS_GENERATOR_11", "LAPIS_GENERATOR_12"], "REDSTONE_GENERATOR_1": ["REDSTONE_GENERATOR_2", "REDSTONE_GENERATOR_3", "REDSTONE_GENERATOR_4", "REDSTONE_GENERATOR_5", "REDSTONE_GENERATOR_6", "REDSTONE_GENERATOR_7", "REDSTONE_GENERATOR_8", "REDSTONE_GENERATOR_9", "REDSTONE_GENERATOR_10", "REDSTONE_GENERATOR_11", "REDSTONE_GENERATOR_12"], "EMERALD_GENERATOR_1": ["EMERALD_GENERATOR_2", "EMERALD_GENERATOR_3", "EMERALD_GENERATOR_4", "EMERALD_GENERATOR_5", "EMERALD_GENERATOR_6", "EMERALD_GENERATOR_7", "EMERALD_GENERATOR_8", "EMERALD_GENERATOR_9", "EMERALD_GENERATOR_10", "EMERALD_GENERATOR_11", "EMERALD_GENERATOR_12"], "QUARTZ_GENERATOR_1": ["QUARTZ_GENERATOR_2", "QUARTZ_GENERATOR_3", "QUARTZ_GENERATOR_4", "QUARTZ_GENERATOR_5", "QUARTZ_GENERATOR_6", "QUARTZ_GENERATOR_7", "QUARTZ_GENERATOR_8", "QUARTZ_GENERATOR_9", "QUARTZ_GENERATOR_10", "QUARTZ_GENERATOR_11", "QUARTZ_GENERATOR_12"], "ENDER_STONE_GENERATOR_1": ["ENDER_STONE_GENERATOR_2", "ENDER_STONE_GENERATOR_3", "ENDER_STONE_GENERATOR_4", "ENDER_STONE_GENERATOR_5", "ENDER_STONE_GENERATOR_6", "ENDER_STONE_GENERATOR_7", "ENDER_STONE_GENERATOR_8", "ENDER_STONE_GENERATOR_9", "ENDER_STONE_GENERATOR_10", "ENDER_STONE_GENERATOR_11"], "WHEAT_GENERATOR_1": ["WHEAT_GENERATOR_2", "WHEAT_GENERATOR_3", "WHEAT_GENERATOR_4", "WHEAT_GENERATOR_5", "WHEAT_GENERATOR_6", "WHEAT_GENERATOR_7", "WHEAT_GENERATOR_8", "WHEAT_GENERATOR_9", "WHEAT_GENERATOR_10", "WHEAT_GENERATOR_11", "WHEAT_GENERATOR_12"], "MELON_GENERATOR_1": ["MELON_GENERATOR_2", "MELON_GENERATOR_3", "MELON_GENERATOR_4", "MELON_GENERATOR_5", "MELON_GENERATOR_6", "MELON_GENERATOR_7", "MELON_GENERATOR_8", "MELON_GENERATOR_9", "MELON_GENERATOR_10", "MELON_GENERATOR_11", "MELON_GENERATOR_12"], "PUMPKIN_GENERATOR_1": ["PUMPKIN_GENERATOR_2", "PUMPKIN_GENERATOR_3", "PUMPKIN_GENERATOR_4", "PUMPKIN_GENERATOR_5", "PUMPKIN_GENERATOR_6", "PUMPKIN_GENERATOR_7", "PUMPKIN_GENERATOR_8", "PUMPKIN_GENERATOR_9", "PUMPKIN_GENERATOR_10", "PUMPKIN_GENERATOR_11", "PUMPKIN_GENERATOR_12"], "CARROT_GENERATOR_1": ["CARROT_GENERATOR_2", "CARROT_GENERATOR_3", "CARROT_GENERATOR_4", "CARROT_GENERATOR_5", "CARROT_GENERATOR_6", "CARROT_GENERATOR_7", "CARROT_GENERATOR_8", "CARROT_GENERATOR_9", "CARROT_GENERATOR_10", "CARROT_GENERATOR_11", "CARROT_GENERATOR_12"], "POTATO_GENERATOR_1": ["POTATO_GENERATOR_2", "POTATO_GENERATOR_3", "POTATO_GENERATOR_4", "POTATO_GENERATOR_5", "POTATO_GENERATOR_6", "POTATO_GENERATOR_7", "POTATO_GENERATOR_8", "POTATO_GENERATOR_9", "POTATO_GENERATOR_10", "POTATO_GENERATOR_11", "POTATO_GENERATOR_12"], "MUSHROOM_GENERATOR_1": ["MUSHROOM_GENERATOR_2", "MUSHROOM_GENERATOR_3", "MUSHROOM_GENERATOR_4", "MUSHROOM_GENERATOR_5", "MUSHROOM_GENERATOR_6", "MUSHROOM_GENERATOR_7", "MUSHROOM_GENERATOR_8", "MUSHROOM_GENERATOR_9", "MUSHROOM_GENERATOR_10", "MUSHROOM_GENERATOR_11", "MUSHROOM_GENERATOR_12"], "CACTUS_GENERATOR_1": ["CACTUS_GENERATOR_2", "CACTUS_GENERATOR_3", "CACTUS_GENERATOR_4", "CACTUS_GENERATOR_5", "CACTUS_GENERATOR_6", "CACTUS_GENERATOR_7", "CACTUS_GENERATOR_8", "CACTUS_GENERATOR_9", "CACTUS_GENERATOR_10", "CACTUS_GENERATOR_11", "CACTUS_GENERATOR_12"], "COCOA_GENERATOR_1": ["COCOA_GENERATOR_2", "COCOA_GENERATOR_3", "COCOA_GENERATOR_4", "COCOA_GENERATOR_5", "COCOA_GENERATOR_6", "COCOA_GENERATOR_7", "COCOA_GENERATOR_8", "COCOA_GENERATOR_9", "COCOA_GENERATOR_10", "COCOA_GENERATOR_11", "COCOA_GENERATOR_12"], "SUGAR_CANE_GENERATOR_1": ["SUGAR_CANE_GENERATOR_2", "SUGAR_CANE_GENERATOR_3", "SUGAR_CANE_GENERATOR_4", "SUGAR_CANE_GENERATOR_5", "SUGAR_CANE_GENERATOR_6", "SUGAR_CANE_GENERATOR_7", "SUGAR_CANE_GENERATOR_8", "SUGAR_CANE_GENERATOR_9", "SUGAR_CANE_GENERATOR_10", "SUGAR_CANE_GENERATOR_11", "SUGAR_CANE_GENERATOR_12"], "NETHER_WARTS_GENERATOR_1": ["NETHER_WARTS_GENERATOR_2", "NETHER_WARTS_GENERATOR_3", "NETHER_WARTS_GENERATOR_4", "NETHER_WARTS_GENERATOR_5", "NETHER_WARTS_GENERATOR_6", "NETHER_WARTS_GENERATOR_7", "NETHER_WARTS_GENERATOR_8", "NETHER_WARTS_GENERATOR_9", "NETHER_WARTS_GENERATOR_10", "NETHER_WARTS_GENERATOR_11", "NETHER_WARTS_GENERATOR_12"], "FLOWER_GENERATOR_1": ["FLOWER_GENERATOR_2", "FLOWER_GENERATOR_3", "FLOWER_GENERATOR_4", "FLOWER_GENERATOR_5", "FLOWER_GENERATOR_6", "FLOWER_GENERATOR_7", "FLOWER_GENERATOR_8", "FLOWER_GENERATOR_9", "FLOWER_GENERATOR_10", "FLOWER_GENERATOR_11", "FLOWER_GENERATOR_12"], "FISHING_GENERATOR_1": ["FISHING_GENERATOR_2", "FISHING_GENERATOR_3", "FISHING_GENERATOR_4", "FISHING_GENERATOR_5", "FISHING_GENERATOR_6", "FISHING_GENERATOR_7", "FISHING_GENERATOR_8", "FISHING_GENERATOR_9", "FISHING_GENERATOR_10", "FISHING_GENERATOR_11"], "ZOMBIE_GENERATOR_1": ["ZOMBIE_GENERATOR_2", "ZOMBIE_GENERATOR_3", "ZOMBIE_GENERATOR_4", "ZOMBIE_GENERATOR_5", "ZOMBIE_GENERATOR_6", "ZOMBIE_GENERATOR_7", "ZOMBIE_GENERATOR_8", "ZOMBIE_GENERATOR_9", "ZOMBIE_GENERATOR_10", "ZOMBIE_GENERATOR_11"], "REVENANT_GENERATOR_1": ["REVENANT_GENERATOR_2", "REVENANT_GENERATOR_3", "REVENANT_GENERATOR_4", "REVENANT_GENERATOR_5", "REVENANT_GENERATOR_6", "REVENANT_GENERATOR_7", "REVENANT_GENERATOR_8", "REVENANT_GENERATOR_9", "REVENANT_GENERATOR_10", "REVENANT_GENERATOR_11", "REVENANT_GENERATOR_12"], "SKELETON_GENERATOR_1": ["SKELETON_GENERATOR_2", "SKELETON_GENERATOR_3", "SKELETON_GENERATOR_4", "SKELETON_GENERATOR_5", "SKELETON_GENERATOR_6", "SKELETON_GENERATOR_7", "SKELETON_GENERATOR_8", "SKELETON_GENERATOR_9", "SKELETON_GENERATOR_10", "SKELETON_GENERATOR_11"], "CREEPER_GENERATOR_1": ["CREEPER_GENERATOR_2", "CREEPER_GENERATOR_3", "CREEPER_GENERATOR_4", "CREEPER_GENERATOR_5", "CREEPER_GENERATOR_6", "CREEPER_GENERATOR_7", "CREEPER_GENERATOR_8", "CREEPER_GENERATOR_9", "CREEPER_GENERATOR_10", "CREEPER_GENERATOR_11"], "SPIDER_GENERATOR_1": ["SPIDER_GENERATOR_2", "SPIDER_GENERATOR_3", "SPIDER_GENERATOR_4", "SPIDER_GENERATOR_5", "SPIDER_GENERATOR_6", "SPIDER_GENERATOR_7", "SPIDER_GENERATOR_8", "SPIDER_GENERATOR_9", "SPIDER_GENERATOR_10", "SPIDER_GENERATOR_11"], "TARANTULA_GENERATOR_1": ["TARANTULA_GENERATOR_2", "TARANTULA_GENERATOR_3", "TARANTULA_GENERATOR_4", "TARANTULA_GENERATOR_5", "TARANTULA_GENERATOR_6", "TARANTULA_GENERATOR_7", "TARANTULA_GENERATOR_8", "TARANTULA_GENERATOR_9", "TARANTULA_GENERATOR_10", "TARANTULA_GENERATOR_11"], "CAVESPIDER_GENERATOR_1": ["CAVESPIDER_GENERATOR_2", "CAVESPIDER_GENERATOR_3", "CAVESPIDER_GENERATOR_4", "CAVESPIDER_GENERATOR_5", "CAVESPIDER_GENERATOR_6", "CAVESPIDER_GENERATOR_7", "CAVESPIDER_GENERATOR_8", "CAVESPIDER_GENERATOR_9", "CAVESPIDER_GENERATOR_10", "CAVESPIDER_GENERATOR_11"], "BLAZE_GENERATOR_1": ["BLAZE_GENERATOR_2", "BLAZE_GENERATOR_3", "BLAZE_GENERATOR_4", "BLAZE_GENERATOR_5", "BLAZE_GENERATOR_6", "BLAZE_GENERATOR_7", "BLAZE_GENERATOR_8", "BLAZE_GENERATOR_9", "BLAZE_GENERATOR_10", "BLAZE_GENERATOR_11", "BLAZE_GENERATOR_12"], "MAGMA_CUBE_GENERATOR_1": ["MAGMA_CUBE_GENERATOR_2", "MAGMA_CUBE_GENERATOR_3", "MAGMA_CUBE_GENERATOR_4", "MAGMA_CUBE_GENERATOR_5", "MAGMA_CUBE_GENERATOR_6", "MAGMA_CUBE_GENERATOR_7", "MAGMA_CUBE_GENERATOR_8", "MAGMA_CUBE_GENERATOR_9", "MAGMA_CUBE_GENERATOR_10", "MAGMA_CUBE_GENERATOR_11", "MAGMA_CUBE_GENERATOR_12"], "ENDERMAN_GENERATOR_1": ["ENDERMAN_GENERATOR_2", "ENDERMAN_GENERATOR_3", "ENDERMAN_GENERATOR_4", "ENDERMAN_GENERATOR_5", "ENDERMAN_GENERATOR_6", "ENDERMAN_GENERATOR_7", "ENDERMAN_GENERATOR_8", "ENDERMAN_GENERATOR_9", "ENDERMAN_GENERATOR_10", "ENDERMAN_GENERATOR_11"], "GHAST_GENERATOR_1": ["GHAST_GENERATOR_2", "GHAST_GENERATOR_3", "GHAST_GENERATOR_4", "GHAST_GENERATOR_5", "GHAST_GENERATOR_6", "GHAST_GENERATOR_7", "GHAST_GENERATOR_8", "GHAST_GENERATOR_9", "GHAST_GENERATOR_10", "GHAST_GENERATOR_11", "GHAST_GENERATOR_12"], "SLIME_GENERATOR_1": ["SLIME_GENERATOR_2", "SLIME_GENERATOR_3", "SLIME_GENERATOR_4", "SLIME_GENERATOR_5", "SLIME_GENERATOR_6", "SLIME_GENERATOR_7", "SLIME_GENERATOR_8", "SLIME_GENERATOR_9", "SLIME_GENERATOR_10", "SLIME_GENERATOR_11"], "COW_GENERATOR_1": ["COW_GENERATOR_2", "COW_GENERATOR_3", "COW_GENERATOR_4", "COW_GENERATOR_5", "COW_GENERATOR_6", "COW_GENERATOR_7", "COW_GENERATOR_8", "COW_GENERATOR_9", "COW_GENERATOR_10", "COW_GENERATOR_11", "COW_GENERATOR_12"], "PIG_GENERATOR_1": ["PIG_GENERATOR_2", "PIG_GENERATOR_3", "PIG_GENERATOR_4", "PIG_GENERATOR_5", "PIG_GENERATOR_6", "PIG_GENERATOR_7", "PIG_GENERATOR_8", "PIG_GENERATOR_9", "PIG_GENERATOR_10", "PIG_GENERATOR_11", "PIG_GENERATOR_12"], "CHICKEN_GENERATOR_1": ["CHICKEN_GENERATOR_2", "CHICKEN_GENERATOR_3", "CHICKEN_GENERATOR_4", "CHICKEN_GENERATOR_5", "CHICKEN_GENERATOR_6", "CHICKEN_GENERATOR_7", "CHICKEN_GENERATOR_8", "CHICKEN_GENERATOR_9", "CHICKEN_GENERATOR_10", "CHICKEN_GENERATOR_11", "CHICKEN_GENERATOR_12"], "SHEEP_GENERATOR_1": ["SHEEP_GENERATOR_2", "SHEEP_GENERATOR_3", "SHEEP_GENERATOR_4", "SHEEP_GENERATOR_5", "SHEEP_GENERATOR_6", "SHEEP_GENERATOR_7", "SHEEP_GENERATOR_8", "SHEEP_GENERATOR_9", "SHEEP_GENERATOR_10", "SHEEP_GENERATOR_11", "SHEEP_GENERATOR_12"], "RABBIT_GENERATOR_1": ["RABBIT_GENERATOR_2", "RABBIT_GENERATOR_3", "RABBIT_GENERATOR_4", "RABBIT_GENERATOR_5", "RABBIT_GENERATOR_6", "RABBIT_GENERATOR_7", "RABBIT_GENERATOR_8", "RABBIT_GENERATOR_9", "RABBIT_GENERATOR_10", "RABBIT_GENERATOR_11", "RABBIT_GENERATOR_12"], "OAK_GENERATOR_1": ["OAK_GENERATOR_2", "OAK_GENERATOR_3", "OAK_GENERATOR_4", "OAK_GENERATOR_5", "OAK_GENERATOR_6", "OAK_GENERATOR_7", "OAK_GENERATOR_8", "OAK_GENERATOR_9", "OAK_GENERATOR_10", "OAK_GENERATOR_11"], "SPRUCE_GENERATOR_1": ["SPRUCE_GENERATOR_2", "SPRUCE_GENERATOR_3", "SPRUCE_GENERATOR_4", "SPRUCE_GENERATOR_5", "SPRUCE_GENERATOR_6", "SPRUCE_GENERATOR_7", "SPRUCE_GENERATOR_8", "SPRUCE_GENERATOR_9", "SPRUCE_GENERATOR_10", "SPRUCE_GENERATOR_11"], "BIRCH_GENERATOR_1": ["BIRCH_GENERATOR_2", "BIRCH_GENERATOR_3", "BIRCH_GENERATOR_4", "BIRCH_GENERATOR_5", "BIRCH_GENERATOR_6", "BIRCH_GENERATOR_7", "BIRCH_GENERATOR_8", "BIRCH_GENERATOR_9", "BIRCH_GENERATOR_10", "BIRCH_GENERATOR_11"], "DARK_OAK_GENERATOR_1": ["DARK_OAK_GENERATOR_2", "DARK_OAK_GENERATOR_3", "DARK_OAK_GENERATOR_4", "DARK_OAK_GENERATOR_5", "DARK_OAK_GENERATOR_6", "DARK_OAK_GENERATOR_7", "DARK_OAK_GENERATOR_8", "DARK_OAK_GENERATOR_9", "DARK_OAK_GENERATOR_10", "DARK_OAK_GENERATOR_11"], "ACACIA_GENERATOR_1": ["ACACIA_GENERATOR_2", "ACACIA_GENERATOR_3", "ACACIA_GENERATOR_4", "ACACIA_GENERATOR_5", "ACACIA_GENERATOR_6", "ACACIA_GENERATOR_7", "ACACIA_GENERATOR_8", "ACACIA_GENERATOR_9", "ACACIA_GENERATOR_10", "ACACIA_GENERATOR_11"], "JUNGLE_GENERATOR_1": ["JUNGLE_GENERATOR_2", "JUNGLE_GENERATOR_3", "JUNGLE_GENERATOR_4", "JUNGLE_GENERATOR_5", "JUNGLE_GENERATOR_6", "JUNGLE_GENERATOR_7", "JUNGLE_GENERATOR_8", "JUNGLE_GENERATOR_9", "JUNGLE_GENERATOR_10", "JUNGLE_GENERATOR_11"], "MITHRIL_GENERATOR_1": ["MITHRIL_GENERATOR_2", "MITHRIL_GENERATOR_3", "MITHRIL_GENERATOR_4", "MITHRIL_GENERATOR_5", "MITHRIL_GENERATOR_6", "MITHRIL_GENERATOR_7", "MITHRIL_GENERATOR_8", "MITHRIL_GENERATOR_9", "MITHRIL_GENERATOR_10", "MITHRIL_GENERATOR_11", "MITHRIL_GENERATOR_12"], "LARGE_AGRONOMY_SACK": ["MEDIUM_AGRONOMY_SACK", "SMALL_AGRONOMY_SACK"], "LARGE_COMBAT_SACK": ["MEDIUM_COMBAT_SACK", "SMALL_COMBAT_SACK"], "LARGE_FISHING_SACK": ["MEDIUM_FISHING_SACK", "SMALL_FISHING_SACK"], "LARGE_FORAGING_SACK": ["MEDIUM_FORAGING_SACK", "SMALL_FORAGING_SACK"], "LARGE_HUSBANDRY_SACK": ["MEDIUM_HUSBANDRY_SACK", "SMALL_HUSBANDRY_SACK"], "LARGE_MINING_SACK": ["MEDIUM_MINING_SACK", "SMALL_MINING_SACK"], "LARGE_SLAYER_SACK": ["MEDIUM_SLAYER_SACK", "SMALL_SLAYER_SACK"], "MITHRIL_GOLEM;5": ["MITHRIL_GOLEM;4", "MITHRIL_GOLEM;3", "MITHRIL_GOLEM;2", "MITHRIL_GOLEM;1", "MITHRIL_GOLEM;0"], "BEACON_1": ["BEACON_2", "BEACON_3", "BEACON_4", "BEACON_5"], "TITANIUM_RELIC": ["TITANIUM_ARTIFACT", "TITANIUM_RING", "TITANIUM_TALISMAN"], "TITANIUM_DRILL_4": ["TITANIUM_DRILL_3", "TITANIUM_DRILL_2", "TITANIUM_DRILL_1"], "MITHRIL_DRILL_2": ["MITHRIL_DRILL_1"], "SIMPLE_CARROT_CANDY": ["GREAT_CARROT_CANDY", "SUPERB_CARROT_CANDY", "ULTIMATE_CARROT_CANDY"], "WHITE_GIFT": ["GREEN_GIFT", "RED_GIFT", "GOLD_GIFT"], "SUPERIOR_BABY": ["STRONG_BABY", "UNSTABLE_BABY", "YOUNG_BABY", "WISE_BABY", "HOLY_BABY", "OLD_BABY", "PROTECTOR_BABY"], "PET_SKIN_SHEEP_BLACK": ["PET_SKIN_SHEEP_PURPLE", "PET_SKIN_SHEEP_PINK", "PET_SKIN_SHEEP_LIGHT_GREEN", "PET_SKIN_SHEEP_LIGHT_BLUE", "PET_SKIN_SHEEP_WHITE", "PET_SKIN_SHEEP_NEON_RED", "PET_SKIN_SHEEP_NEON_GREEN", "PET_SKIN_SHEEP_NEON_BLUE", "PET_SKIN_SHEEP_NEON_YELLOW"], "PET_SKIN_ELEPHANT_RED": ["PET_SKIN_ELEPHANT_GREEN", "PET_SKIN_ELEPHANT_BLUE", "PET_SKIN_ELEPHANT_PURPLE", "PET_SKIN_ELEPHANT_PINK", "PET_SKIN_ELEPHANT_ORANGE"], "PET_SKIN_ROCK_COOL": ["PET_SKIN_ROCK_DERP", "PET_SKIN_ROCK_LAUGH", "PET_SKIN_ROCK_SMILE", "PET_SKIN_ROCK_THINKING", "PET_SKIN_ROCK_EMBARRASSED"], "PET_SKIN_DRAGON_NEON_RED": ["PET_SKIN_DRAGON_NEON_BLUE", "PET_SKIN_DRAGON_NEON_PURPLE", "PET_SKIN_DRAGON_PASTEL"], "CAMPFIRE_TALISMAN_1": ["CAMPFIRE_TALISMAN_2", "CAMPFIRE_TALISMAN_3", "CAMPFIRE_TALISMAN_4", "CAMPFIRE_TALISMAN_5", "CAMPFIRE_TALISMAN_6", "CAMPFIRE_TALISMAN_7", "CAMPFIRE_TALISMAN_8", "CAMPFIRE_TALISMAN_9", "CAMPFIRE_TALISMAN_10", "CAMPFIRE_TALISMAN_11", "CAMPFIRE_TALISMAN_12", "CAMPFIRE_TALISMAN_13", "CAMPFIRE_TALISMAN_14", "CAMPFIRE_TALISMAN_15", "CAMPFIRE_TALISMAN_16", "CAMPFIRE_TALISMAN_17", "CAMPFIRE_TALISMAN_18", "CAMPFIRE_TALISMAN_19", "CAMPFIRE_TALISMAN_20", "CAMPFIRE_TALISMAN_21", "CAMPFIRE_TALISMAN_22", "CAMPFIRE_TALISMAN_23", "CAMPFIRE_TALISMAN_24", "CAMPFIRE_TALISMAN_25", "CAMPFIRE_TALISMAN_26", "CAMPFIRE_TALISMAN_27", "CAMPFIRE_TALISMAN_28", "CAMPFIRE_TALISMAN_29"], "PERSONAL_DELETOR_7000": ["PERSONAL_DELETOR_6000", "PERSONAL_DELETOR_5000", "PERSONAL_DELETOR_4000"], "GRANDMA_WOLF;4": ["GRANDMA_WOLF;3", "GRANDMA_WOLF;2", "GRANDMA_WOLF;1", "GRANDMA_WOLF;0"], "VENOMS_TOUCH": ["STARRED_VENOMS_TOUCH"], "SPIDER_QUEEN_STINGER": ["STARRED_SPIDER_QUEEN_STINGER"], "SHADOW_ASSASSIN_ADMIRAL": ["SHADOW_ASSASSIN_MAUVE", "SHADOW_ASSASSIN_CRIMSON"], "PURPLE_EGG": ["GREEN_EGG", "BLUE_EGG"], "PET_SKIN_RABBIT_AQUAMARINE": ["PET_SKIN_RABBIT_ROSE"], "ARMOR_OF_THE_RESISTANCE_BOOTS": ["GENERALS_ARMOR_OF_THE_RESISTANCE_BOOTS"], "ARMOR_OF_THE_RESISTANCE_LEGGINGS": ["GENERALS_ARMOR_OF_THE_RESISTANCE_LEGGINGS"], "ARMOR_OF_THE_RESISTANCE_CHESTPLATE": ["GENERALS_ARMOR_OF_THE_RESISTANCE_CHESTPLATE"], "ARMOR_OF_THE_RESISTANCE_HELMET": ["GENERALS_ARMOR_OF_THE_RESISTANCE_HELMET"], "HOPE_OF_THE_RESISTANCE": ["GENERALS_HOPE_OF_THE_RESISTANCE"], "SUPERIOR_ENDER_DRAGON_BOSS": ["STRONG_ENDER_DRAGON_BOSS", "WISE_ENDER_DRAGON_BOSS", "YOUNG_ENDER_DRAGON_BOSS", "UNSTABLE_ENDER_DRAGON_BOSS", "OLD_ENDER_DRAGON_BOSS", "PROTECTOR_ENDER_DRAGON_BOSS"], "PET_SKIN_BLACK_CAT_ONYX": ["PET_SKIN_BLACK_CAT_IVORY"], "FIRST_MASTER_STAR": ["SECOND_MASTER_STAR", "THIRD_MASTER_STAR", "FOURTH_MASTER_STAR", "FIFTH_MASTER_STAR"], "MASTER_SKULL_TIER_7": ["MASTER_SKULL_TIER_6", "MASTER_SKULL_TIER_5", "MASTER_SKULL_TIER_4", "MASTER_SKULL_TIER_3", "MASTER_SKULL_TIER_2", "MASTER_SKULL_TIER_1"], "YELLOW_ROCK": ["POORLY_WRAPPED_ROCK", "ROSE_BOUQUET", "FAKE_EMERALD_ALTAR", "WARTS_STEW", "MOON_CHUNK", "WRAPPED_GIFT_FOR_JULIETTE", "SOLVED_PRISM", "POEM_OF_INFINITE_LOVE", "FLOWER_MAELSTROM", "VERY_OFFICIAL_YELLOW_ROCK"], "FROZEN_ADVENTURER_MINIBOSS": ["SUPERIOR_LOST_ADVENTURER_MINIBOSS", "HOLY_LOST_ADVENTURER_MINIBOSS", "UNSTABLE_LOST_ADVENTURER_MINIBOSS", "YOUNG_LOST_ADVENTURER_MINIBOSS"], "SPIKED_ATROCITY": ["BAIT_RING"], "MANA_STEAL;1": ["MANA_STEAL;2", "MANA_STEAL;3"], "VOIDLING_GENERATOR_1": ["VOIDLING_GENERATOR_2", "VOIDLING_GENERATOR_3", "VOIDLING_GENERATOR_4", "VOIDLING_GENERATOR_5", "VOIDLING_GENERATOR_6", "VOIDLING_GENERATOR_7", "VOIDLING_GENERATOR_8", "VOIDLING_GENERATOR_9", "VOIDLING_GENERATOR_10", "VOIDLING_GENERATOR_11"], "SMARTY_PANTS;1": ["SMARTY_PANTS;2", "SMARTY_PANTS;3", "SMARTY_PANTS;4", "SMARTY_PANTS;5"], "SOULFLOW_SUPERCELL": ["SOULFLOW_BATTERY", "SOULFLOW_PILE"], "WATER_BUCKET": ["WATER", "STATIONARY_WATER"], "LAVA_BUCKET": ["LAVA", "STATIONARY_LAVA"], "VOIDWALKER_KATANA": ["VOIDEDGE_KATANA", "VORPAL_KATANA", "ATOMSPLIT_KATANA"], "UNDEAD_SWORD": ["REVENANT_SWORD", "REAPER_SWORD"], "SHAMAN_SWORD": ["POOCH_SWORD"], "ENDER_RELIC": ["ENDER_ARTIFACT"], "SCORPIUS_SPECIAL_MAYOR_MONSTER": ["DERPY_SPECIAL_MAYOR_MONSTER", "JERRY_SPECIAL_MAYOR_MONSTER", "DANTE_SPECIAL_MAYOR_MONSTER"], "AATROX_MAYOR_MONSTER": ["COLE_MAYOR_MONSTER", "DIANA_MAYOR_MONSTER", "DIAZ_MAYOR_MONSTER", "FINNEGAN_MAYOR_MONSTER", "FOXY_MAYOR_MONSTER", "MARINA_MAYOR_MONSTER", "PAUL_MAYOR_MONSTER"], "VOIDGLOOM_SERAPH_1_BOSS": ["VOIDGLOOM_SERAPH_2_BOSS", "VOIDGLOOM_SERAPH_3_BOSS", "VOIDGLOOM_SERAPH_4_BOSS"], "SVEN_PACKMASTER_1_BOSS": ["SVEN_PACKMASTER_2_BOSS", "SVEN_PACKMASTER_3_BOSS", "SVEN_PACKMASTER_4_BOSS"], "TARANTULA_BROODFATHER_1_BOSS": ["TARANTULA_BROODFATHER_2_BOSS", "TARANTULA_BROODFATHER_3_BOSS", "TARANTULA_BROODFATHER_4_BOSS"], "PET_SKIN_JERRY_GREEN_ELF": ["PET_SKIN_JERRY_RED_ELF"], "HARD_STONE_GENERATOR_1": ["HARD_STONE_GENERATOR_2", "HARD_STONE_GENERATOR_3", "HARD_STONE_GENERATOR_4", "HARD_STONE_GENERATOR_5", "HARD_STONE_GENERATOR_6", "HARD_STONE_GENERATOR_7", "HARD_STONE_GENERATOR_8", "HARD_STONE_GENERATOR_9", "HARD_STONE_GENERATOR_10", "HARD_STONE_GENERATOR_11", "HARD_STONE_GENERATOR_12"], "GEMSTONE_DRILL_4": ["GEMSTONE_DRILL_3", "GEMSTONE_DRILL_2", "GEMSTONE_DRILL_1"], "PERFECT_RUBY_GEM": ["FLAWLESS_RUBY_GEM", "FINE_RUBY_GEM", "FLAWED_RUBY_GEM", "ROUGH_RUBY_GEM"], "PERFECT_SAPPHIRE_GEM": ["FLAWLESS_SAPPHIRE_GEM", "FINE_SAPPHIRE_GEM", "FLAWED_SAPPHIRE_GEM", "ROUGH_SAPPHIRE_GEM"], "PERFECT_JASPER_GEM": ["FLAWLESS_JASPER_GEM", "FINE_JASPER_GEM", "FLAWED_JASPER_GEM", "ROUGH_JASPER_GEM"], "PERFECT_TOPAZ_GEM": ["FLAWLESS_TOPAZ_GEM", "FINE_TOPAZ_GEM", "FLAWED_TOPAZ_GEM", "ROUGH_TOPAZ_GEM"], "PERFECT_AMBER_GEM": ["FLAWLESS_AMBER_GEM", "FINE_AMBER_GEM", "FLAWED_AMBER_GEM", "ROUGH_AMBER_GEM"], "PERFECT_JADE_GEM": ["FLAWLESS_JADE_GEM", "FINE_JADE_GEM", "FLAWED_JADE_GEM", "ROUGH_JADE_GEM"], "PERFECT_AMETHYST_GEM": ["FLAWLESS_AMETHYST_GEM", "FINE_AMETHYST_GEM", "FLAWED_AMETHYST_GEM", "ROUGH_AMETHYST_GEM"], "BAL;4": ["BAL;3"], "AMBER_POWER_SCROLL": ["AMETHYST_POWER_SCROLL", "JASPER_POWER_SCROLL", "RUBY_POWER_SCROLL", "SAPPHIRE_POWER_SCROLL", "OPAL_POWER_SCROLL"], "EXTRA_LARGE_GEMSTONE_SACK": ["LARGE_GEMSTONE_SACK", "MEDIUM_GEMSTONE_SACK", "SMALL_GEMSTONE_SACK"], "PRISTINE;1": ["PRISTINE;2", "PRISTINE;3", "PRISTINE;4", "PRISTINE;5"], "POWER_RELIC": ["POWER_ARTIFACT", "POWER_RING", "POWER_TALISMAN"], "SUPERIOR_SHIMMER": ["STRONG_SHIMMER", "UNSTABLE_SHIMMER", "YOUNG_SHIMMER", "WISE_SHIMMER", "HOLY_SHIMMER", "OLD_SHIMMER", "PROTECTOR_SHIMMER"], "BOSS_CORLEONE_BOSS": ["EXECUTIVE_WENDY_MONSTER", "EXECUTIVE_VIPER_MONSTER", "EXECUTIVE_SEBASTIAN_MONSTER", "GRUNT_MONSTER"], "SCATHA;4": ["SCATHA;3", "SCATHA;2", "SCATHA;1", "SCATHA;0"], "REVENANT_HORROR_1_BOSS": ["REVENANT_HORROR_2_BOSS", "REVENANT_HORROR_3_BOSS", "REVENANT_HORROR_4_BOSS", "REVENANT_HORROR_5_BOSS"], "BOOSTER_COOKIE": ["FREE_COOKIE", "REFUND_COOKIE"], "PET_SKIN_DOLPHIN_SNUBNOSE_GREEN": ["PET_SKIN_DOLPHIN_SNUBNOSE_RED", "PET_SKIN_DOLPHIN_SNUBNOSE_PURPLE"], "DUNGEON_DISC_1": ["DUNGEON_DISC_2", "DUNGEON_DISC_3", "DUNGEON_DISC_4", "DUNGEON_DISC_5"], "JUMBO_BACKPACK": ["GREATER_BACKPACK", "LARGE_BACKPACK", "MEDIUM_BACKPACK", "SMALL_BACKPACK"], "XXLARGE_ENCHANTED_CHEST": ["XLARGE_ENCHANTED_CHEST", "LARGE_ENCHANTED_CHEST", "MEDIUM_ENCHANTED_CHEST", "SMALL_ENCHANTED_CHEST"], "BINGO;5": ["BINGO;4", "BINGO;3", "BINGO;2", "BINGO;1", "BINGO;0"], "RED_SAND_GENERATOR_1": ["RED_SAND_GENERATOR_2", "RED_SAND_GENERATOR_3", "RED_SAND_GENERATOR_4", "RED_SAND_GENERATOR_5", "RED_SAND_GENERATOR_6", "RED_SAND_GENERATOR_7", "RED_SAND_GENERATOR_8", "RED_SAND_GENERATOR_9", "RED_SAND_GENERATOR_10", "RED_SAND_GENERATOR_11", "RED_SAND_GENERATOR_12"], "MYCELIUM_GENERATOR_1": ["MYCELIUM_GENERATOR_2", "MYCELIUM_GENERATOR_3", "MYCELIUM_GENERATOR_4", "MYCELIUM_GENERATOR_5", "MYCELIUM_GENERATOR_6", "MYCELIUM_GENERATOR_7", "MYCELIUM_GENERATOR_8", "MYCELIUM_GENERATOR_9", "MYCELIUM_GENERATOR_10", "MYCELIUM_GENERATOR_11", "MYCELIUM_GENERATOR_12"], "LAMP_RAINBOW": ["LAMP_LIGHT_BLUE", "LAMP_CYAN", "LAMP_BLUE", "LAMP_LIME", "LAMP_GREEN", "LAMP_PINK", "LAMP_LILAC", "LAMP_MAGENTA", "LAMP_PURPLE", "LAMP_YELLOW", "LAMP_WHITE", "LAMP_RED", "LAMP_ORANGE", "LAMP_LIGHT_GRAY", "LAMP_GRAY", "LAMP_BROWN", "LAMP_BLACK"], "INFERNO_GENERATOR_1": ["INFERNO_GENERATOR_2", "INFERNO_GENERATOR_3", "INFERNO_GENERATOR_4", "INFERNO_GENERATOR_5", "INFERNO_GENERATOR_6", "INFERNO_GENERATOR_7", "INFERNO_GENERATOR_8", "INFERNO_GENERATOR_9", "INFERNO_GENERATOR_10", "INFERNO_GENERATOR_11"], "AURORA_HELMET": ["HOT_AURORA_HELMET", "BURNING_AURORA_HELMET", "FIERY_AURORA_HELMET", "INFERNAL_AURORA_HELMET"], "AURORA_CHESTPLATE": ["HOT_AURORA_CHESTPLATE", "BURNING_AURORA_CHESTPLATE", "FIERY_AURORA_CHESTPLATE", "INFERNAL_AURORA_CHESTPLATE"], "AURORA_LEGGINGS": ["HOT_AURORA_LEGGINGS", "BURNING_AURORA_LEGGINGS", "FIERY_AURORA_LEGGINGS", "INFERNAL_AURORA_LEGGINGS"], "AURORA_BOOTS": ["HOT_AURORA_BOOTS", "BURNING_AURORA_BOOTS", "FIERY_AURORA_BOOTS", "INFERNAL_AURORA_BOOTS"], "CRIMSON_HELMET": ["HOT_CRIMSON_HELMET", "BURNING_CRIMSON_HELMET", "FIERY_CRIMSON_HELMET", "INFERNAL_CRIMSON_HELMET"], "CRIMSON_CHESTPLATE": ["HOT_CRIMSON_CHESTPLATE", "BURNING_CRIMSON_CHESTPLATE", "FIERY_CRIMSON_CHESTPLATE", "INFERNAL_CRIMSON_CHESTPLATE"], "CRIMSON_LEGGINGS": ["HOT_CRIMSON_LEGGINGS", "BURNING_CRIMSON_LEGGINGS", "FIERY_CRIMSON_LEGGINGS", "INFERNAL_CRIMSON_LEGGINGS"], "CRIMSON_BOOTS": ["HOT_CRIMSON_BOOTS", "BURNING_CRIMSON_BOOTS", "FIERY_CRIMSON_BOOTS", "INFERNAL_CRIMSON_BOOTS"], "TERROR_HELMET": ["HOT_TERROR_HELMET", "BURNING_TERROR_HELMET", "FIERY_TERROR_HELMET", "INFERNAL_TERROR_HELMET"], "TERROR_CHESTPLATE": ["HOT_TERROR_CHESTPLATE", "BURNING_TERROR_CHESTPLATE", "FIERY_TERROR_CHESTPLATE", "INFERNAL_TERROR_CHESTPLATE"], "TERROR_LEGGINGS": ["HOT_TERROR_LEGGINGS", "BURNING_TERROR_LEGGINGS", "FIERY_TERROR_LEGGINGS", "INFERNAL_TERROR_LEGGINGS"], "TERROR_BOOTS": ["HOT_TERROR_BOOTS", "BURNING_TERROR_BOOTS", "FIERY_TERROR_BOOTS", "INFERNAL_TERROR_BOOTS"], "FERVOR_HELMET": ["HOT_FERVOR_HELMET", "BURNING_FERVOR_HELMET", "FIERY_FERVOR_HELMET", "INFERNAL_FERVOR_HELMET"], "FERVOR_CHESTPLATE": ["HOT_FERVOR_CHESTPLATE", "BURNING_FERVOR_CHESTPLATE", "FIERY_FERVOR_CHESTPLATE", "INFERNAL_FERVOR_CHESTPLATE"], "FERVOR_LEGGINGS": ["HOT_FERVOR_LEGGINGS", "BURNING_FERVOR_LEGGINGS", "FIERY_FERVOR_LEGGINGS", "INFERNAL_FERVOR_LEGGINGS"], "FERVOR_BOOTS": ["HOT_FERVOR_BOOTS", "BURNING_FERVOR_BOOTS", "FIERY_FERVOR_BOOTS", "INFERNAL_FERVOR_BOOTS"], "DOJO_BLACK_BELT": ["DOJO_BROWN_BELT", "DOJO_BLUE_BELT", "DOJO_GREEN_BELT", "DOJO_YELLOW_BELT", "DOJO_WHITE_BELT"], "LARGE_LAVA_FISHING_SACK": ["MEDIUM_LAVA_FISHING_SACK", "SMALL_LAVA_FISHING_SACK"], "LARGE_NETHER_SACK": ["MEDIUM_NETHER_SACK", "SMALL_NETHER_SACK"], "INFERNO_HYPERGOLIC_MAGMA_CREAM": ["INFERNO_HEAVY_MAGMA_CREAM", "INFERNO_FUEL_MAGMA_CREAM"], "INFERNO_HYPERGOLIC_BLAZE_ROD": ["INFERNO_HEAVY_BLAZE_ROD", "INFERNO_FUEL_BLAZE_ROD"], "INFERNO_HYPERGOLIC_CRUDE_GABAGOOL": ["INFERNO_HEAVY_CRUDE_GABAGOOL", "INFERNO_FUEL_CRUDE_GABAGOOL"], "INFERNO_HYPERGOLIC_GLOWSTONE_DUST": ["INFERNO_HEAVY_GLOWSTONE_DUST", "INFERNO_FUEL_GLOWSTONE_DUST"], "INFERNO_HYPERGOLIC_NETHER_STALK": ["INFERNO_HEAVY_NETHER_STALK", "INFERNO_FUEL_NETHER_STALK"], "BLOBFISH_DIAMOND": ["BLOBFISH_GOLD", "BLOBFISH_SILVER", "BLOBFISH_BRONZE"], "FLYFISH_DIAMOND": ["FLYFISH_GOLD", "FLYFISH_SILVER", "FLYFISH_BRONZE"], "GOLDEN_FISH_DIAMOND": ["GOLDEN_FISH_GOLD", "GOLDEN_FISH_SILVER", "GOLDEN_FISH_BRONZE"], "GUSHER_DIAMOND": ["GUSHER_GOLD", "GUSHER_SILVER", "GUSHER_BRONZE"], "MOLDFIN_DIAMOND": ["MOLDFIN_GOLD", "MOLDFIN_SILVER", "MOLDFIN_BRONZE"], "SKELETON_FISH_DIAMOND": ["SKELETON_FISH_GOLD", "SKELETON_FISH_SILVER", "SKELETON_FISH_BRONZE"], "SLUGFISH_DIAMOND": ["SLUGFISH_GOLD", "SLUGFISH_SILVER", "SLUGFISH_BRONZE"], "SOUL_FISH_DIAMOND": ["SOUL_FISH_GOLD", "SOUL_FISH_SILVER", "SOUL_FISH_BRONZE"], "STEAMING_HOT_FLOUNDER_DIAMOND": ["STEAMING_HOT_FLOUNDER_GOLD", "STEAMING_HOT_FLOUNDER_SILVER", "STEAMING_HOT_FLOUNDER_BRONZE"], "VANILLE_DIAMOND": ["VANILLE_GOLD", "VANILLE_SILVER", "VANILLE_BRONZE"], "VOLCANIC_STONEFISH_DIAMOND": ["VOLCANIC_STONEFISH_GOLD", "VOLCANIC_STONEFISH_SILVER", "VOLCANIC_STONEFISH_BRONZE"], "SULPHUR_SKITTER_DIAMOND": ["SULPHUR_SKITTER_GOLD", "SULPHUR_SKITTER_SILVER", "SULPHUR_SKITTER_BRONZE"], "LAVA_HORSE_DIAMOND": ["LAVA_HORSE_GOLD", "LAVA_HORSE_SILVER", "LAVA_HORSE_BRONZE"], "OBFUSCATED_FISH_1_DIAMOND": ["OBFUSCATED_FISH_1_GOLD", "OBFUSCATED_FISH_1_SILVER", "OBFUSCATED_FISH_1_BRONZE"], "MANA_RAY_DIAMOND": ["MANA_RAY_GOLD", "MANA_RAY_SILVER", "MANA_RAY_BRONZE"], "ABIPHONE_FLIP_DRAGON": ["ABIPHONE_FLIP_NUCLEUS", "ABIPHONE_FLIP_VOLCANO", "ABIPHONE_XIV_ENORMOUS_BLACK", "ABIPHONE_XIV_ENORMOUS", "ABIPHONE_XIV_ENORMOUS_PURPLE", "ABIPHONE_XIII_PRO_GIGA", "ABIPHONE_XIII_PRO", "ABIPHONE_XII_MEGA_COLOR", "ABIPHONE_XII_MEGA", "ABIPHONE_XI_ULTRA_STYLE", "ABIPHONE_XI_ULTRA", "ABIPHONE_X_PLUS_SPECIAL_EDITION", "ABIPHONE_X_PLUS"], "SNAIL;4": ["SNAIL;3", "SNAIL;2", "SNAIL;1", "SNAIL;0"], "MOOSHROOM_COW;4": ["MOOSHROOM_COW;3", "MOOSHROOM_COW;2", "MOOSHROOM_COW;1", "MOOSHROOM_COW;0"], "MAGMA_FISH": ["MAGMA_FISH_SILVER", "MAGMA_FISH_GOLD", "MAGMA_FISH_DIAMOND"], "HELLFIRE_ROD": ["INFERNO_ROD", "MAGMA_ROD"], "ROD_OF_THE_SEA": ["LEGEND_ROD", "CHAMP_ROD", "CHALLENGE_ROD"], "CHARM;1": ["CHARM;2", "CHARM;3", "CHARM;4", "CHARM;5"], "STRONG_MANA;1": ["STRONG_MANA;2", "STRONG_MANA;3", "STRONG_MANA;4", "STRONG_MANA;5", "STRONG_MANA;6", "STRONG_MANA;7", "STRONG_MANA;8", "STRONG_MANA;9", "STRONG_MANA;10"], "MANA_VAMPIRE;1": ["MANA_VAMPIRE;2", "MANA_VAMPIRE;3", "MANA_VAMPIRE;4", "MANA_VAMPIRE;5", "MANA_VAMPIRE;6", "MANA_VAMPIRE;7", "MANA_VAMPIRE;8", "MANA_VAMPIRE;9", "MANA_VAMPIRE;10"], "FEROCIOUS_MANA;1": ["FEROCIOUS_MANA;2", "FEROCIOUS_MANA;3", "FEROCIOUS_MANA;4", "FEROCIOUS_MANA;5", "FEROCIOUS_MANA;6", "FEROCIOUS_MANA;7", "FEROCIOUS_MANA;8", "FEROCIOUS_MANA;9", "FEROCIOUS_MANA;10"], "HARDENED_MANA;1": ["HARDENED_MANA;2", "HARDENED_MANA;3", "HARDENED_MANA;4", "HARDENED_MANA;5", "HARDENED_MANA;6", "HARDENED_MANA;7", "HARDENED_MANA;8", "HARDENED_MANA;9", "HARDENED_MANA;10"], "RAINBOW;1": ["RAINBOW;2", "RAINBOW;3"], "CORRUPTION;1": ["CORRUPTION;2", "CORRUPTION;3", "CORRUPTION;4", "CORRUPTION;5"], "HOLLOW_HELMET": ["HOT_HOLLOW_HELMET", "BURNING_HOLLOW_HELMET", "FIERY_HOLLOW_HELMET", "INFERNAL_HOLLOW_HELMET"], "HOLLOW_CHESTPLATE": ["HOT_HOLLOW_CHESTPLATE", "BURNING_HOLLOW_CHESTPLATE", "FIERY_HOLLOW_CHESTPLATE", "INFERNAL_HOLLOW_CHESTPLATE"], "HOLLOW_LEGGINGS": ["HOT_HOLLOW_LEGGINGS", "BURNING_HOLLOW_LEGGINGS", "FIERY_HOLLOW_LEGGINGS", "INFERNAL_HOLLOW_LEGGINGS"], "HOLLOW_BOOTS": ["HOT_HOLLOW_BOOTS", "BURNING_HOLLOW_BOOTS", "FIERY_HOLLOW_BOOTS", "INFERNAL_HOLLOW_BOOTS"], "KARATE_FISH_DIAMOND": ["KARATE_FISH_GOLD", "KARATE_FISH_SILVER", "KARATE_FISH_BRONZE"], "SMOLDERING;1": ["SMOLDERING;2", "SMOLDERING;3", "SMOLDERING;4", "SMOLDERING;5"], "ODGERS_DIAMOND_TOOTH": ["ODGERS_GOLD_TOOTH", "ODGERS_SILVER_TOOTH", "ODGERS_BRONZE_TOOTH"], "KUUDRA;4": ["KUUDRA;3", "KUUDRA;2", "KUUDRA;1", "KUUDRA;0"], "UPGRADE_STONE_SUBZERO": ["UPGRADE_STONE_GLACIAL", "UPGRADE_STONE_FROST"], "KUUDRA_INFERNAL_TIER_KEY": ["KUUDRA_FIERY_TIER_KEY", "KUUDRA_BURNING_TIER_KEY", "KUUDRA_HOT_TIER_KEY", "KUUDRA_TIER_KEY"], "BINGO_RELIC": ["BINGO_ARTIFACT", "BINGO_RING", "BINGO_TALISMAN"], "HUNTER_RING": ["HUNTER_TALISMAN"], "BITE_RUNE;1": ["BITE_RUNE;2", "BITE_RUNE;3"], "BLOOD_2_RUNE;1": ["BLOOD_2_RUNE;2", "BLOOD_2_RUNE;3"], "CLOUDS_RUNE;1": ["CLOUDS_RUNE;2", "CLOUDS_RUNE;3"], "COUTURE_RUNE;1": ["COUTURE_RUNE;2", "COUTURE_RUNE;3"], "DRAGON_RUNE;1": ["DRAGON_RUNE;2", "DRAGON_RUNE;3"], "ENCHANT_RUNE;1": ["ENCHANT_RUNE;2", "ENCHANT_RUNE;3"], "ENDERSNAKE_RUNE;1": ["ENDERSNAKE_RUNE;2", "ENDERSNAKE_RUNE;3"], "FIRE_SPIRAL_RUNE;1": ["FIRE_SPIRAL_RUNE;2", "FIRE_SPIRAL_RUNE;3"], "GEM_RUNE;1": ["GEM_RUNE;2", "GEM_RUNE;3"], "GOLDEN_RUNE;1": ["GOLDEN_RUNE;2", "GOLDEN_RUNE;3"], "HEARTS_RUNE;1": ["HEARTS_RUNE;2", "HEARTS_RUNE;3"], "HOT_RUNE;1": ["HOT_RUNE;2", "HOT_RUNE;3"], "ICE_RUNE;1": ["ICE_RUNE;2", "ICE_RUNE;3"], "LAVA_RUNE;1": ["LAVA_RUNE;2", "LAVA_RUNE;3"], "LIGHTNING_RUNE;1": ["LIGHTNING_RUNE;2", "LIGHTNING_RUNE;3"], "MAGIC_RUNE;1": ["MAGIC_RUNE;2", "MAGIC_RUNE;3"], "MUSIC_RUNE;1": ["MUSIC_RUNE;2", "MUSIC_RUNE;3"], "RAINBOW_RUNE;1": ["RAINBOW_RUNE;2", "RAINBOW_RUNE;3"], "REDSTONE_RUNE;1": ["REDSTONE_RUNE;2", "REDSTONE_RUNE;3"], "SLIMY_RUNE;1": ["SLIMY_RUNE;2", "SLIMY_RUNE;3"], "SMOKEY_RUNE;1": ["SMOKEY_RUNE;2", "SMOKEY_RUNE;3"], "SNAKE_RUNE;1": ["SNAKE_RUNE;2", "SNAKE_RUNE;3"], "SNOW_RUNE;1": ["SNOW_RUNE;2", "SNOW_RUNE;3"], "SPARKLING_RUNE;1": ["SPARKLING_RUNE;2", "SPARKLING_RUNE;3"], "SPIRIT_RUNE;1": ["SPIRIT_RUNE;2", "SPIRIT_RUNE;3"], "TIDAL_RUNE;1": ["TIDAL_RUNE;2", "TIDAL_RUNE;3"], "WAKE_RUNE;1": ["WAKE_RUNE;2", "WAKE_RUNE;3"], "WHITE_SPIRAL_RUNE;1": ["WHITE_SPIRAL_RUNE;2", "WHITE_SPIRAL_RUNE;3"], "ZAP_RUNE;1": ["ZAP_RUNE;2", "ZAP_RUNE;3"], "ZOMBIE_SLAYER_RUNE;1": ["ZOMBIE_SLAYER_RUNE;2", "ZOMBIE_SLAYER_RUNE;3"], "PARTY_HAT_CRAB_AQUA": ["PARTY_HAT_CRAB_BLACK", "PARTY_HAT_CRAB_GREEN", "PARTY_HAT_CRAB_LIME", "PARTY_HAT_CRAB_ORANGE", "PARTY_HAT_CRAB_PINK", "PARTY_HAT_CRAB_PURPLE", "PARTY_HAT_CRAB_RED", "PARTY_HAT_CRAB_YELLOW"], "AMETHYST_CRYSTAL": ["AMBER_CRYSTAL", "JADE_CRYSTAL", "RUBY_CRYSTAL", "SAPPHIRE_CRYSTAL", "TOPAZ_CRYSTAL", "JASPER_CRYSTAL", "PERIDOT_CRYSTAL", "AQUAMARINE_CRYSTAL", "CITRINE_CRYSTAL", "ONYX_CRYSTAL", "OPAL_CRYSTAL"], "LAVATEARS_RUNE;1": ["LAVATEARS_RUNE;2", "LAVATEARS_RUNE;3"], "FIERY_BURST_RUNE;1": ["FIERY_BURST_RUNE;2", "FIERY_BURST_RUNE;3"], "OBFUSCATED_FISH_2_DIAMOND": ["OBFUSCATED_FISH_2_GOLD", "OBFUSCATED_FISH_2_SILVER", "OBFUSCATED_FISH_2_BRONZE"], "OBFUSCATED_FISH_3_DIAMOND": ["OBFUSCATED_FISH_3_GOLD", "OBFUSCATED_FISH_3_SILVER", "OBFUSCATED_FISH_3_BRONZE"], "FLAME;1": ["FLAME;2"], "ULTIMATE_FLASH;1": ["ULTIMATE_FLASH;2", "ULTIMATE_FLASH;3", "ULTIMATE_FLASH;4", "ULTIMATE_FLASH;5"], "ULTIMATE_FATAL_TEMPO;1": ["ULTIMATE_FATAL_TEMPO;2", "ULTIMATE_FATAL_TEMPO;3", "ULTIMATE_FATAL_TEMPO;4", "ULTIMATE_FATAL_TEMPO;5"], "ULTIMATE_INFERNO;1": ["ULTIMATE_INFERNO;2", "ULTIMATE_INFERNO;3", "ULTIMATE_INFERNO;4", "ULTIMATE_INFERNO;5"], "BURSTSTOPPER_ARTIFACT": ["BURSTSTOPPER_TALISMAN"], "PERFECT_OPAL_GEM": ["FLAWLESS_OPAL_GEM", "FINE_OPAL_GEM", "FLAWED_OPAL_GEM", "ROUGH_OPAL_GEM"], "INFERNO_DEMONLORD_1_BOSS": ["INFERNO_DEMONLORD_2_BOSS", "INFERNO_DEMONLORD_3_BOSS", "INFERNO_DEMONLORD_4_BOSS"], "SUBZERO_WISP;4": ["GLACIAL_WISP;3", "FROST_WISP;2", "DROPLET_WISP;1"], "AMALGAMATED_CRIMSONITE_NEW": ["AMALGAMATED_CRIMSONITE"], "GOLDOR_THE_FISH": ["STORM_THE_FISH", "MAXOR_THE_FISH"], "DIVINE_GIFT;1": ["DIVINE_GIFT;2", "DIVINE_GIFT;3"], "PET_SKIN_ROCK_SUS": ["PET_SKIN_ROCK_BLINKING", "PET_SKIN_ROCK_SURPRISED", "PET_SKIN_ROCK_SWEATING"], "CAYENNE;4": ["CAYENNE;5"], "ULTIMATE_HABANERO_TACTICS;4": ["ULTIMATE_HABANERO_TACTICS;5"], "TABASCO;2": ["TABASCO;3"], "ASPECT_OF_THE_LEECH_1": ["ASPECT_OF_THE_LEECH_2", "ASPECT_OF_THE_LEECH_3"], "RIFT_NECKLACE_INSIDE_1": ["RIFT_NECKLACE_INSIDE_2", "RIFT_NECKLACE_INSIDE_3"], "ESSENCE_WITHER": ["ESSENCE_CRIMSON", "ESSENCE_DIAMOND", "ESSENCE_DRAGON", "ESSENCE_GOLD", "ESSENCE_ICE", "ESSENCE_SPIDER", "ESSENCE_UNDEAD", "ESSENCE_SUN_GECKO"], "RAT;5": ["RAT;4"], "PISCARY;1": ["PISCARY;2", "PISCARY;3", "PISCARY;4", "PISCARY;5", "PISCARY;6"], "WATER_ORB": ["LAVA_WATER_ORB", "SPOOKY_WATER_ORB", "SHARK_WATER_ORB", "WINTER_WATER_ORB"], "GILLSPLASH_BELT": ["FINWAVE_BELT", "ICHTHYIC_BELT"], "GILLSPLASH_CLOAK": ["FINWAVE_CLOAK", "ICHTHYIC_CLOAK"], "GILLSPLASH_GLOVES": ["FINWAVE_GLOVES", "ICHTHYIC_GLOVES"], "ULTIMATE_BOBBIN_TIME;3": ["ULTIMATE_BOBBIN_TIME;4", "ULTIMATE_BOBBIN_TIME;5"], "GREAT_SPOOK_ARTIFACT": ["GREAT_SPOOK_RING", "GREAT_SPOOK_TALISMAN"], "PET_SKIN_SHEEP_CHROMA_WOOLY": ["PET_SKIN_SHEEP_BLACK_WOOLY", "PET_SKIN_SHEEP_WHITE_WOOLY"], "INFERNAL_KUUDRA_CORE": ["FIERY_KUUDRA_CORE", "BURNING_KUUDRA_CORE"], "PARTY_HAT_CRAB_AQUA_ANIMATED": ["PARTY_HAT_CRAB_BLACK_ANIMATED", "PARTY_HAT_CRAB_GREEN_ANIMATED", "PARTY_HAT_CRAB_LIME_ANIMATED", "PARTY_HAT_CRAB_ORANGE_ANIMATED", "PARTY_HAT_CRAB_PINK_ANIMATED", "PARTY_HAT_CRAB_PURPLE_ANIMATED", "PARTY_HAT_CRAB_RED_ANIMATED", "PARTY_HAT_CRAB_YELLOW_ANIMATED"], "PARTY_HAT_SLOTH_FLUSHED": ["PARTY_HAT_SLOTH_HAPPY", "PARTY_HAT_SLOTH_CHEEKY", "PARTY_HAT_SLOTH_COOL", "PARTY_HAT_SLOTH_CUTE", "PARTY_HAT_SLOTH_DERP", "PARTY_HAT_SLOTH_GRUMPY", "PARTY_HAT_SLOTH_REGULAR", "PARTY_HAT_SLOTH_SHOCK", "PARTY_HAT_SLOTH_TEARS"], "DRACONIC_ARTIFACT": ["DRACONIC_RING", "DRACONIC_TALISMAN"], "ABICASE_BLUE_RED": ["ABICASE_BLUE_BLUE", "ABICASE_BLUE_GREEN", "ABICASE_BLUE_YELLOW", "ABICASE_BLUE_AQUA"], "ABICASE_SUMSUNG_1": ["ABICASE_SUMSUNG_2"], "VACCINE_ARTIFACT": ["VACCINE_RING", "VACCINE_TALISMAN"], "GLACIAL_ARTIFACT": ["GLACIAL_RING", "GLACIAL_TALISMAN"], "GOLD_GIFT_TALISMAN": ["PURPLE_GIFT_TALISMAN", "BLUE_GIFT_TALISMAN", "GREEN_GIFT_TALISMAN", "WHITE_GIFT_TALISMAN"], "PROSPERITY;1": ["PROSPERITY;2", "PROSPERITY;3", "PROSPERITY;4", "PROSPERITY;5"], "UNCOMMON_KUUDRA_CHUNK": ["RARE_KUUDRA_CHUNK", "EPIC_KUUDRA_CHUNK", "LEGENDARY_KUUDRA_CHUNK"], "MAXOR_CELESTIAL": ["GOLDOR_CELESTIAL", "NECRON_CELESTIAL", "STORM_CELESTIAL"], "PET_SKIN_SCATHA_ALBINO": ["PET_SKIN_SCATHA_GOLDEN", "PET_SKIN_SCATHA_DARK"], "SUNDER;1": ["SUNDER;2", "SUNDER;3", "SUNDER;4", "SUNDER;5", "SUNDER;6"], "MELON_DICER_3": ["MELON_DICER_2", "MELON_DICER"], "PUMPKIN_DICER_3": ["PUMPKIN_DICER_2", "PUMPKIN_DICER"], "GLISTENING_MELON_FLUX": ["GOLDEN_APPLE_FLUX", "GLOWING_GRAPE_FLUX"], "GREEN_THUMB;1": ["GREEN_THUMB;2", "GREEN_THUMB;3", "GREEN_THUMB;4", "GREEN_THUMB;5"], "DEDICATION;1": ["DEDICATION;2", "DEDICATION;3", "DEDICATION;4"], "LARGE_DRAGON_SACK": ["MEDIUM_DRAGON_SACK", "SMALL_DRAGON_SACK"], "KUUDRA_FOLLOWER_RELIC": ["KUUDRA_FOLLOWER_ARTIFACT"], "DANTE_TALISMAN": ["DANTE_RING"], "CRUX_TALISMAN_7": ["CRUX_TALISMAN_6", "CRUX_TALISMAN_5", "CRUX_TALISMAN_4", "CRUX_TALISMAN_3", "CRUX_TALISMAN_2", "CRUX_TALISMAN_1"], "AGARIMOO_ARTIFACT": ["AGARIMOO_RING", "AGARIMOO_TALISMAN"], "LUSH_ARTIFACT": ["LUSH_RING", "LUSH_TALISMAN"], "REFLECTION;1": ["REFLECTION;2", "REFLECTION;3", "REFLECTION;4", "REFLECTION;5"], "QUANTUM;3": ["QUANTUM;4", "QUANTUM;5"], "BLOOD_DONOR_ARTIFACT": ["BLOOD_DONOR_RING", "BLOOD_DONOR_TALISMAN"], "VAMPIRE_GENERATOR_1": ["VAMPIRE_GENERATOR_2", "VAMPIRE_GENERATOR_3", "VAMPIRE_GENERATOR_4", "VAMPIRE_GENERATOR_5", "VAMPIRE_GENERATOR_6", "VAMPIRE_GENERATOR_7", "VAMPIRE_GENERATOR_8", "VAMPIRE_GENERATOR_9", "VAMPIRE_GENERATOR_10", "VAMPIRE_GENERATOR_11", "VAMPIRE_GENERATOR_12"], "TRANSYLVANIAN;4": ["TRANSYLVANIAN;5"], "SOULTWIST_RUNE;1": ["SOULTWIST_RUNE;2", "SOULTWIST_RUNE;3"], "GLYPH_CONCLAMATUS": ["GLYPH_CONCLAMATUS", "GLYPH_FIRMITAS", "GLYPH_FORTIS", "GLYPH_PERNIMIUS", "GLYPH_POTENTIA", "GLYPH_ROBUR", "GLYPH_VALIDUS", "GLYPH_VIS"], "ARGOFAY_TRAFFICKER_1_RIFT_NPC": ["ARGOFAY_TRAFFICKER_2_RIFT_NPC", "ARGOFAY_TRAFFICKER_3_RIFT_NPC", "ARGOFAY_TRAFFICKER_4_RIFT_NPC"], "ARGOFAY_THREEBROTHER_1_RIFT_NPC": ["ARGOFAY_THREEBROTHER_2_RIFT_NPC", "ARGOFAY_THREEBROTHER_3_RIFT_NPC"], "HEALING_MELON": ["JUICY_HEALING_MELON", "LUSCIOUS_HEALING_MELON"], "RIFTSTALKER_BLOODFIEND_1_BOSS": ["RIFTSTALKER_BLOODFIEND_2_BOSS", "RIFTSTALKER_BLOODFIEND_3_BOSS", "RIFTSTALKER_BLOODFIEND_4_BOSS", "RIFTSTALKER_BLOODFIEND_5_BOSS"], "CENTURY_MEMENTO_GREEN": ["CENTURY_MEMENTO_PINK", "CENTURY_MEMENTO_BLUE", "CENTURY_MEMENTO_YELLOW", "CENTURY_MEMENTO_RED"], "ADAPTIVE_BELT": ["STARRED_ADAPTIVE_BELT"], "BAT_WAND": ["STARRED_BAT_WAND"], "BONE_BOMMERANG": ["STARRED_BONE_BOMMERANG"], "FELTHORN_REAPER": ["STARRED_FELTHORN_REAPER"], "ITEM_SPIRIT_BOW": ["STARRED_ITEM_SPIRIT_BOW"], "SHADOW_ASSASSIN_CLOAK": ["STARRED_SHADOW_ASSASSIN_CLOAK"], "SPIRIT_MASK": ["STARRED_SPIRIT_MASK"], "THORNS_BOOTS": ["STARRED_THORNS_BOOTS"], "PET_SKIN_BABY_YETI_MIDNIGHT": ["PET_SKIN_BABY_YETI_LIGHT_SASQUATCH", "PET_SKIN_BABY_YETI_DARK_SASQUATCH"], "EERIE;4": ["EERIE;2", "EERIE;0"], "BLACK_CAT;5": ["BLACK_CAT;4"], "ANITA_ARTIFACT": ["ANITA_RING", "ANITA_TALISMAN"], "SKYMART_VACUUM": ["SKYMART_TURBO_VACUUM", "SKYMART_HYPER_VACUUM", "INFINI_VACUUM", "INFINI_VACUUM_HOOVERIUS"], "PESTHUNTER_ARTIFACT": ["PESTHUNTER_RING", "PESTHUNTER_BADGE"], "VINYL_PRETTY_FLY": ["VINYL_CRICKET_CHOIR", "VINYL_EARTHWORM_ENSEMBLE", "VINYL_SLOW_AND_GROOVY", "VINYL_BEETLE", "VINYL_CICADA_SYMPHONY", "VINYL_DYNAMITES", "VINYL_RODENT_REVOLUTION", "VINYL_WINGS_OF_HARMONY", "VINYL_BUZZIN_BEATS"], "PESTERMINATOR;1": ["PESTERMINATOR;2", "PESTERMINATOR;3", "PESTERMINATOR;4", "PESTERMINATOR;5"], "PET_SKIN_RAT_GYM_RAT": ["PET_SKIN_RAT_SQUEAKHEART", "PET_SKIN_RAT_MR_CLAWS", "PET_SKIN_RAT_KARATE", "PET_SKIN_RAT_RAT-STRONAUT", "PET_SKIN_RAT_SECRAT_SERVICE", "PET_SKIN_RAT_NINJA", "PET_SKIN_RAT_SECURATY_GUARD", "PET_SKIN_RAT_JUNK_RAT", "PET_SKIN_RAT_PIRATE"], "SLUG;4": ["SLUG;3"], "PET_SKIN_ENDERMAN_BLUE_MOON": ["PET_SKIN_ENDERMAN_SUN", "PET_SKIN_ENDERMAN_NEBULA"], "YETI_SWORD": ["STARRED_YETI_SWORD"], "ICE_SPRAY_WAND": ["STARRED_ICE_SPRAY_WAND"], "GLACIAL_SCYTHE": ["STARRED_GLACIAL_SCYTHE"], "ULTIMATE_REFRIGERATE;1": ["ULTIMATE_REFRIGERATE;2", "ULTIMATE_REFRIGERATE;3", "ULTIMATE_REFRIGERATE;4", "ULTIMATE_REFRIGERATE;5"], "ULTIMATE_THE_ONE;4": ["ULTIMATE_THE_ONE;5"], "SNOWMAN;5": ["SNOWMAN;4"], "PET_SKIN_SLUG_AZURE_SEA_SLUG": ["PET_SKIN_SLUG_LEAF_SHEEP_SEA_SLUG", "PET_SKIN_SLUG_VIOLET_SEA_SLUG"], "GIFT_GOLD_BACKPACK": ["GIFT_PURPLE_BACKPACK", "GIFT_GREEN_BACKPACK", "GIFT_BLUE_BACKPACK", "GIFT_BLACK_BACKPACK", "GIFT_WHITE_BACKPACK"], "BEAR_BED": ["BEAR_SIDE_TABLE", "BEAR_CHAIR", "BEAR_BOOKSHELF", "BEAR_BENCH"], "BONE_NECKLACE": ["STARRED_BONE_NECKLACE"], "RECORD_3": ["RECORD_4", "RECORD_5", "RECORD_6", "RECORD_7", "RECORD_8", "RECORD_9", "RECORD_10", "RECORD_11", "RECORD_12", "GOLD_RECORD", "GREEN_RECORD"], "PET_SKIN_TARANTULA_PINK": ["PET_SKIN_SPIDER_BLACK_WIDOW", "PET_SKIN_TARANTULA_GREENBOTTLE", "PET_SKIN_SPIDER_PEACOCK"], "PERFECT_PERIDOT_GEM": ["FLAWLESS_PERIDOT_GEM", "FINE_PERIDOT_GEM", "FLAWED_PERIDOT_GEM", "ROUGH_PERIDOT_GEM"], "PERFECT_CITRINE_GEM": ["FLAWLESS_CITRINE_GEM", "FINE_CITRINE_GEM", "FLAWED_CITRINE_GEM", "ROUGH_CITRINE_GEM"], "PERFECT_AQUAMARINE_GEM": ["FLAWLESS_AQUAMARINE_GEM", "FINE_AQUAMARINE_GEM", "FLAWED_AQUAMARINE_GEM", "ROUGH_AQUAMARINE_GEM"], "PERFECT_ONYX_GEM": ["FLAWLESS_ONYX_GEM", "FINE_ONYX_GEM", "FLAWED_ONYX_GEM", "ROUGH_ONYX_GEM"], "WEBBED_FOSSIL": ["UGLY_FOSSIL", "TUSK_FOSSIL", "SPINE_FOSSIL", "CLAW_FOSSIL", "FOOTPRINT_FOSSIL", "CLUBBED_FOSSIL", "HELIX"], "PALEONTOLOGIST;1": ["PALEONTOLOGIST;2", "PALEONTOLOGIST;3", "PALEONTOLOGIST;4", "PALEONTOLOGIST;5"], "ICE_COLD;1": ["ICE_COLD;2", "ICE_COLD;3", "ICE_COLD;4", "ICE_COLD;5"], "GLACITE_GOLEM;4": ["GLACITE_GOLEM;3"], "PRESTIGE_CHOCOLATE_REALM": ["GANACHE_CHOCOLATE_SLAB", "RICH_CHOCOLATE_CHUNK", "SMOOTH_CHOCOLATE_BAR", "NIBBLE_CHOCOLATE_STICK"], "POTION_DUNGEON;1": ["POTION_DUNGEON;2", "POTION_DUNGEON;3", "POTION_DUNGEON;4", "POTION_DUNGEON;5", "POTION_DUNGEON;6", "POTION_DUNGEON;7"], "POTION_ALCHEMY_XP_BOOST;1": ["POTION_ALCHEMY_XP_BOOST;2", "POTION_ALCHEMY_XP_BOOST;3"], "POTION_MINING_XP_BOOST;1": ["POTION_MINING_XP_BOOST;2", "POTION_MINING_XP_BOOST;3"], "POTION_FORAGING_XP_BOOST;1": ["POTION_FORAGING_XP_BOOST;2", "POTION_FORAGING_XP_BOOST;3"], "POTION_ENCHANTING_XP_BOOST;1": ["POTION_ENCHANTING_XP_BOOST;2", "POTION_ENCHANTING_XP_BOOST;3"], "POTION_FARMING_XP_BOOST;1": ["POTION_FARMING_XP_BOOST;2", "POTION_FARMING_XP_BOOST;3"], "POTION_FISHING_XP_BOOST;1": ["POTION_FISHING_XP_BOOST;2", "POTION_FISHING_XP_BOOST;3"], "POTION_COMBAT_XP_BOOST;1": ["POTION_COMBAT_XP_BOOST;2", "POTION_COMBAT_XP_BOOST;3"], "POTION_ABSORPTION;1": ["POTION_ABSORPTION;2", "POTION_ABSORPTION;3", "POTION_ABSORPTION;4", "POTION_ABSORPTION;5", "POTION_ABSORPTION;6", "POTION_ABSORPTION;7", "POTION_ABSORPTION;8"], "POTION_ADRENALINE;1": ["POTION_ADRENALINE;2", "POTION_ADRENALINE;3", "POTION_ADRENALINE;4", "POTION_ADRENALINE;5", "POTION_ADRENALINE;6", "POTION_ADRENALINE;7", "POTION_ADRENALINE;8"], "POTION_AGILITY;1": ["POTION_AGILITY;2", "POTION_AGILITY;3", "POTION_AGILITY;4"], "POTION_ARCHERY;1": ["POTION_ARCHERY;2", "POTION_ARCHERY;3", "POTION_ARCHERY;4"], "POTION_BLINDNESS;1": ["POTION_BLINDNESS;2", "POTION_BLINDNESS;3", "POTION_BLINDNESS;4"], "POTION_BURNING;1": ["POTION_BURNING;2", "POTION_BURNING;3", "POTION_BURNING;4"], "POTION_DAMAGE;1": ["POTION_DAMAGE;2", "POTION_DAMAGE;3", "POTION_DAMAGE;4", "POTION_DAMAGE;5", "POTION_DAMAGE;6", "POTION_DAMAGE;7", "POTION_DAMAGE;8"], "POTION_COLD_RESISTANCE;1": ["POTION_COLD_RESISTANCE;2", "POTION_COLD_RESISTANCE;3", "POTION_COLD_RESISTANCE;4"], "POTION_EXPERIENCE;1": ["POTION_EXPERIENCE;2", "POTION_EXPERIENCE;3", "POTION_EXPERIENCE;4"], "POTION_DODGE;1": ["POTION_DODGE;2", "POTION_DODGE;3", "POTION_DODGE;4"], "POTION_HASTE;1": ["POTION_HASTE;2", "POTION_HASTE;3", "POTION_HASTE;4"], "POTION_JUMP_BOOST;1": ["POTION_JUMP_BOOST;2", "POTION_JUMP_BOOST;3", "POTION_JUMP_BOOST;4"], "POTION_HEALING;1": ["POTION_HEALING;2", "POTION_HEALING;3", "POTION_HEALING;4", "POTION_HEALING;5", "POTION_HEALING;6", "POTION_HEALING;7", "POTION_HEALING;8"], "POTION_SLOWNESS;1": ["POTION_SLOWNESS;2", "POTION_SLOWNESS;3", "POTION_SLOWNESS;4", "POTION_SLOWNESS;5", "POTION_SLOWNESS;6", "POTION_SLOWNESS;7", "POTION_SLOWNESS;8"], "POTION_RESISTANCE;1": ["POTION_RESISTANCE;2", "POTION_RESISTANCE;3", "POTION_RESISTANCE;4", "POTION_RESISTANCE;5", "POTION_RESISTANCE;6", "POTION_RESISTANCE;7", "POTION_RESISTANCE;8"], "POTION_REGENERATION;1": ["POTION_REGENERATION;2", "POTION_REGENERATION;3", "POTION_REGENERATION;4", "POTION_REGENERATION;5", "POTION_REGENERATION;6", "POTION_REGENERATION;7", "POTION_REGENERATION;8", "POTION_REGENERATION;9"], "POTION_MANA;1": ["POTION_MANA;2", "POTION_MANA;3", "POTION_MANA;4", "POTION_MANA;5", "POTION_MANA;6", "POTION_MANA;7", "POTION_MANA;8"], "POTION_NIGHT_VISION;1": ["POTION_NIGHT_VISION;2"], "POTION_POISON;1": ["POTION_POISON;2", "POTION_POISON;3", "POTION_POISON;4"], "POTION_PET_LUCK;1": ["POTION_PET_LUCK;2", "POTION_PET_LUCK;3", "POTION_PET_LUCK;4"], "POTION_WATER_BREATHING;1": ["POTION_WATER_BREATHING;2", "POTION_WATER_BREATHING;3", "POTION_WATER_BREATHING;4", "POTION_WATER_BREATHING;5", "POTION_WATER_BREATHING;6"], "POTION_STRENGTH;1": ["POTION_STRENGTH;2", "POTION_STRENGTH;3", "POTION_STRENGTH;4", "POTION_STRENGTH;5", "POTION_STRENGTH;6", "POTION_STRENGTH;7", "POTION_STRENGTH;8"], "POTION_SPEED;1": ["POTION_SPEED;2", "POTION_SPEED;3", "POTION_SPEED;4", "POTION_SPEED;5", "POTION_SPEED;6", "POTION_SPEED;7", "POTION_SPEED;8"], "POTION_SPELUNKER;1": ["POTION_SPELUNKER;2", "POTION_SPELUNKER;3", "POTION_SPELUNKER;4", "POTION_SPELUNKER;5"], "POTION_STUN;1": ["POTION_STUN;2", "POTION_STUN;3", "POTION_STUN;4"], "POTION_STAMINA;1": ["POTION_STAMINA;2", "POTION_STAMINA;3", "POTION_STAMINA;4"], "POTION_TRUE_DEFENSE;1": ["POTION_TRUE_DEFENSE;2", "POTION_TRUE_DEFENSE;3", "POTION_TRUE_DEFENSE;4"], "POTION_WOUNDED;1": ["POTION_WOUNDED;2", "POTION_WOUNDED;3", "POTION_WOUNDED;4"], "POTION_WEAKNESS;1": ["POTION_WEAKNESS;2", "POTION_WEAKNESS;3", "POTION_WEAKNESS;4", "POTION_WEAKNESS;5", "POTION_WEAKNESS;6", "POTION_WEAKNESS;7", "POTION_WEAKNESS;8"], "POTION_MAGIC_FIND;1": ["POTION_MAGIC_FIND;2", "POTION_MAGIC_FIND;3", "POTION_MAGIC_FIND;4"], "POTION_SPIRIT;1": ["POTION_SPIRIT;2", "POTION_SPIRIT;3", "POTION_SPIRIT;4"], "RELIC_OF_COINS": ["ARTIFACT_OF_COINS", "RING_OF_COINS", "COIN_TALISMAN"], "EMERALD_ARTIFACT": ["EMERALD_RING"], "MIDAS_STAFF": ["STARRED_MIDAS_STAFF"], "MIDAS_SWORD": ["STARRED_MIDAS_SWORD"], "CROWN_OF_GREED": ["CROWN_OF_AVARICE"], "GLOSSY_MINERAL_TALISMAN": ["MINERAL_TALISMAN"], "SCAVENGER_ARTIFACT": ["SCAVENGER_RING", "SCAVENGER_TALISMAN"], "BALLOON_HAT_2024_AQUA": ["BALLOON_HAT_2024_PINK", "BALLOON_HAT_2024_RED", "BALLOON_HAT_2024_GREEN", "BALLOON_HAT_2024_ORANGE", "BALLOON_HAT_2024_LIME", "BALLOON_HAT_2024_PURPLE", "BALLOON_HAT_2024_BLACK", "BALLOON_HAT_2024_YELLOW"], "PET_SKIN_AMMONITE_HERMIT_GRAPHITE": ["PET_SKIN_AMMONITE_HERMIT_BEACH_BALL", "PET_SKIN_AMMONITE_HERMIT_SAND_CASTLE", "PET_SKIN_AMMONITE_HERMIT_PAUA_SHELL", "PET_SKIN_AMMONITE_HERMIT_BAKED_BEANS"], "POTION_RABBIT;1": ["POTION_RABBIT;2", "POTION_RABBIT;3", "POTION_RABBIT;4", "POTION_RABBIT;5", "POTION_RABBIT;6"], "ULTIMATE_FLOWSTATE;1": ["ULTIMATE_FLOWSTATE;2", "ULTIMATE_FLOWSTATE;3"], "DAEDALUS_AXE": ["STARRED_DAEDALUS_AXE"], "BONE_BOOMERANG": ["STARRED_BONE_BOOMERANG"], "HASTE_ARTIFACT": ["HASTE_RING"], "LAPIDARY;1": ["LAPIDARY;2", "LAPIDARY;3", "LAPIDARY;4", "LAPIDARY;5"], "GARDEN_SCYTHE": ["SAM_SCYTHE"], "ADVANCED_GARDENING_HOE": ["BASIC_GARDENING_HOE"], "ADVANCED_GARDENING_AXE": ["BASIC_GARDENING_AXE"], "PEST_REPELLENT_MAX": ["PEST_REPELLENT"], "BIOHAZARD_HELMET": ["MUSHROOM_HELMET"], "BIOHAZARD_SUIT": ["MUSHROOM_CHESTPLATE"], "BIOHAZARD_LEGGINGS": ["MUSHROOM_LEGGINGS"], "BIOHAZARD_BOOTS": ["MUSHROOM_BOOTS"], "PEST_MOUSE_TRAP": ["PEST_TRAP"], "HOLIDAY_CHEST_ENDER_BACKPACK": ["HOLIDAY_CHEST_RED_BACKPACK", "HOLIDAY_CHEST_G<PERSON><PERSON>_BACKPACK", "HOLIDAY_CHEST_YELLOW_BACKPACK", "HOLIDAY_CHEST_TEAL_BACKPACK"], "POTION_VENOMOUS;1": ["POTION_VENOMOUS;2", "POTION_VENOMOUS;3", "POTION_VENOMOUS;4"], "POTION_KNOCKBACK;1": ["POTION_KNOCKBACK;2", "POTION_KNOCKBACK;3", "POTION_KNOCKBACK;4"], "POTION_CRITICAL;1": ["POTION_CRITICAL;2", "POTION_CRITICAL;3", "POTION_CRITICAL;4"], "ATTRIBUTE_SHARD_MIDAS_TOUCH;1": ["ATTRIBUTE_SHARD_MIDAS_TOUCH;2", "ATTRIBUTE_SHARD_MIDAS_TOUCH;3", "ATTRIBUTE_SHARD_MIDAS_TOUCH;4", "ATTRIBUTE_SHARD_MIDAS_TOUCH;5", "ATTRIBUTE_SHARD_MIDAS_TOUCH;6", "ATTRIBUTE_SHARD_MIDAS_TOUCH;7", "ATTRIBUTE_SHARD_MIDAS_TOUCH;8", "ATTRIBUTE_SHARD_MIDAS_TOUCH;9", "ATTRIBUTE_SHARD_MIDAS_TOUCH;10"], "ATTRIBUTE_SHARD_BREEZE;1": ["ATTRIBUTE_SHARD_BREEZE;2", "ATTRIBUTE_SHARD_BREEZE;3", "ATTRIBUTE_SHARD_BREEZE;4", "ATTRIBUTE_SHARD_BREEZE;5", "ATTRIBUTE_SHARD_BREEZE;6", "ATTRIBUTE_SHARD_BREEZE;7", "ATTRIBUTE_SHARD_BREEZE;8", "ATTRIBUTE_SHARD_BREEZE;9", "ATTRIBUTE_SHARD_BREEZE;10"], "ATTRIBUTE_SHARD_UNDEAD;1": ["ATTRIBUTE_SHARD_UNDEAD;2", "ATTRIBUTE_SHARD_UNDEAD;3", "ATTRIBUTE_SHARD_UNDEAD;4", "ATTRIBUTE_SHARD_UNDEAD;5", "ATTRIBUTE_SHARD_UNDEAD;6", "ATTRIBUTE_SHARD_UNDEAD;7", "ATTRIBUTE_SHARD_UNDEAD;8", "ATTRIBUTE_SHARD_UNDEAD;9", "ATTRIBUTE_SHARD_UNDEAD;10"], "ATTRIBUTE_SHARD_FISHERMAN;1": ["ATTRIBUTE_SHARD_FISHERMAN;2", "ATTRIBUTE_SHARD_FISHERMAN;3", "ATTRIBUTE_SHARD_FISHERMAN;4", "ATTRIBUTE_SHARD_FISHERMAN;5", "ATTRIBUTE_SHARD_FISHERMAN;6", "ATTRIBUTE_SHARD_FISHERMAN;7", "ATTRIBUTE_SHARD_FISHERMAN;8", "ATTRIBUTE_SHARD_FISHERMAN;9", "ATTRIBUTE_SHARD_FISHERMAN;10"], "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;1": ["ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;2", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;3", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;4", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;5", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;6", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;7", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;8", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;9", "ATTRIBUTE_SHARD_UNDEAD_RESISTANCE;10"], "ATTRIBUTE_SHARD_DOUBLE_HOOK;1": ["ATTRIBUTE_SHARD_DOUBLE_HOOK;2", "ATTRIBUTE_SHARD_DOUBLE_HOOK;3", "ATTRIBUTE_SHARD_DOUBLE_HOOK;4", "ATTRIBUTE_SHARD_DOUBLE_HOOK;5", "ATTRIBUTE_SHARD_DOUBLE_HOOK;6", "ATTRIBUTE_SHARD_DOUBLE_HOOK;7", "ATTRIBUTE_SHARD_DOUBLE_HOOK;8", "ATTRIBUTE_SHARD_DOUBLE_HOOK;9", "ATTRIBUTE_SHARD_DOUBLE_HOOK;10"], "ATTRIBUTE_SHARD_INFECTION;1": ["ATTRIBUTE_SHARD_INFECTION;2", "ATTRIBUTE_SHARD_INFECTION;3", "ATTRIBUTE_SHARD_INFECTION;4", "ATTRIBUTE_SHARD_INFECTION;5", "ATTRIBUTE_SHARD_INFECTION;6", "ATTRIBUTE_SHARD_INFECTION;7", "ATTRIBUTE_SHARD_INFECTION;8", "ATTRIBUTE_SHARD_INFECTION;9", "ATTRIBUTE_SHARD_INFECTION;10"], "ATTRIBUTE_SHARD_HUNTER;1": ["ATTRIBUTE_SHARD_HUNTER;2", "ATTRIBUTE_SHARD_HUNTER;3", "ATTRIBUTE_SHARD_HUNTER;4", "ATTRIBUTE_SHARD_HUNTER;5", "ATTRIBUTE_SHARD_HUNTER;6", "ATTRIBUTE_SHARD_HUNTER;7", "ATTRIBUTE_SHARD_HUNTER;8", "ATTRIBUTE_SHARD_HUNTER;9", "ATTRIBUTE_SHARD_HUNTER;10"], "ATTRIBUTE_SHARD_LIFE_REGENERATION;1": ["ATTRIBUTE_SHARD_LIFE_REGENERATION;2", "ATTRIBUTE_SHARD_LIFE_REGENERATION;3", "ATTRIBUTE_SHARD_LIFE_REGENERATION;4", "ATTRIBUTE_SHARD_LIFE_REGENERATION;5", "ATTRIBUTE_SHARD_LIFE_REGENERATION;6", "ATTRIBUTE_SHARD_LIFE_REGENERATION;7", "ATTRIBUTE_SHARD_LIFE_REGENERATION;8", "ATTRIBUTE_SHARD_LIFE_REGENERATION;9", "ATTRIBUTE_SHARD_LIFE_REGENERATION;10"], "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;1": ["ATTRIBUTE_SHARD_FISHING_EXPERIENCE;2", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;3", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;4", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;5", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;6", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;7", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;8", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;9", "ATTRIBUTE_SHARD_FISHING_EXPERIENCE;10"], "ATTRIBUTE_SHARD_ARACHNO;1": ["ATTRIBUTE_SHARD_ARACHNO;2", "ATTRIBUTE_SHARD_ARACHNO;3", "ATTRIBUTE_SHARD_ARACHNO;4", "ATTRIBUTE_SHARD_ARACHNO;5", "ATTRIBUTE_SHARD_ARACHNO;6", "ATTRIBUTE_SHARD_ARACHNO;7", "ATTRIBUTE_SHARD_ARACHNO;8", "ATTRIBUTE_SHARD_ARACHNO;9", "ATTRIBUTE_SHARD_ARACHNO;10"], "ATTRIBUTE_SHARD_TROPHY_HUNTER;1": ["ATTRIBUTE_SHARD_TROPHY_HUNTER;2", "ATTRIBUTE_SHARD_TROPHY_HUNTER;3", "ATTRIBUTE_SHARD_TROPHY_HUNTER;4", "ATTRIBUTE_SHARD_TROPHY_HUNTER;5", "ATTRIBUTE_SHARD_TROPHY_HUNTER;6", "ATTRIBUTE_SHARD_TROPHY_HUNTER;7", "ATTRIBUTE_SHARD_TROPHY_HUNTER;8", "ATTRIBUTE_SHARD_TROPHY_HUNTER;9", "ATTRIBUTE_SHARD_TROPHY_HUNTER;10"], "ATTRIBUTE_SHARD_ELITE;1": ["ATTRIBUTE_SHARD_ELITE;2", "ATTRIBUTE_SHARD_ELITE;3", "ATTRIBUTE_SHARD_ELITE;4", "ATTRIBUTE_SHARD_ELITE;5", "ATTRIBUTE_SHARD_ELITE;6", "ATTRIBUTE_SHARD_ELITE;7", "ATTRIBUTE_SHARD_ELITE;8", "ATTRIBUTE_SHARD_ELITE;9", "ATTRIBUTE_SHARD_ELITE;10"], "ATTRIBUTE_SHARD_MANA_POOL;1": ["ATTRIBUTE_SHARD_MANA_POOL;2", "ATTRIBUTE_SHARD_MANA_POOL;3", "ATTRIBUTE_SHARD_MANA_POOL;4", "ATTRIBUTE_SHARD_MANA_POOL;5", "ATTRIBUTE_SHARD_MANA_POOL;6", "ATTRIBUTE_SHARD_MANA_POOL;7", "ATTRIBUTE_SHARD_MANA_POOL;8", "ATTRIBUTE_SHARD_MANA_POOL;9", "ATTRIBUTE_SHARD_MANA_POOL;10"], "ATTRIBUTE_SHARD_SPEED;1": ["ATTRIBUTE_SHARD_SPEED;2", "ATTRIBUTE_SHARD_SPEED;3", "ATTRIBUTE_SHARD_SPEED;4", "ATTRIBUTE_SHARD_SPEED;5", "ATTRIBUTE_SHARD_SPEED;6", "ATTRIBUTE_SHARD_SPEED;7", "ATTRIBUTE_SHARD_SPEED;8", "ATTRIBUTE_SHARD_SPEED;9", "ATTRIBUTE_SHARD_SPEED;10"], "ATTRIBUTE_SHARD_BLAZING_FORTUNE;1": ["ATTRIBUTE_SHARD_BLAZING_FORTUNE;2", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;3", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;4", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;5", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;6", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;7", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;8", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;9", "ATTRIBUTE_SHARD_BLAZING_FORTUNE;10"], "ATTRIBUTE_SHARD_COMBO;1": ["ATTRIBUTE_SHARD_COMBO;2", "ATTRIBUTE_SHARD_COMBO;3", "ATTRIBUTE_SHARD_COMBO;4", "ATTRIBUTE_SHARD_COMBO;5", "ATTRIBUTE_SHARD_COMBO;6", "ATTRIBUTE_SHARD_COMBO;7", "ATTRIBUTE_SHARD_COMBO;8", "ATTRIBUTE_SHARD_COMBO;9", "ATTRIBUTE_SHARD_COMBO;10"], "ATTRIBUTE_SHARD_ATTACK_SPEED;1": ["ATTRIBUTE_SHARD_ATTACK_SPEED;2", "ATTRIBUTE_SHARD_ATTACK_SPEED;3", "ATTRIBUTE_SHARD_ATTACK_SPEED;4", "ATTRIBUTE_SHARD_ATTACK_SPEED;5", "ATTRIBUTE_SHARD_ATTACK_SPEED;6", "ATTRIBUTE_SHARD_ATTACK_SPEED;7", "ATTRIBUTE_SHARD_ATTACK_SPEED;8", "ATTRIBUTE_SHARD_ATTACK_SPEED;9", "ATTRIBUTE_SHARD_ATTACK_SPEED;10"], "ATTRIBUTE_SHARD_FISHING_SPEED;1": ["ATTRIBUTE_SHARD_FISHING_SPEED;2", "ATTRIBUTE_SHARD_FISHING_SPEED;3", "ATTRIBUTE_SHARD_FISHING_SPEED;4", "ATTRIBUTE_SHARD_FISHING_SPEED;5", "ATTRIBUTE_SHARD_FISHING_SPEED;6", "ATTRIBUTE_SHARD_FISHING_SPEED;7", "ATTRIBUTE_SHARD_FISHING_SPEED;8", "ATTRIBUTE_SHARD_FISHING_SPEED;9", "ATTRIBUTE_SHARD_FISHING_SPEED;10"], "ATTRIBUTE_SHARD_LIFE_RECOVERY;1": ["ATTRIBUTE_SHARD_LIFE_RECOVERY;2", "ATTRIBUTE_SHARD_LIFE_RECOVERY;3", "ATTRIBUTE_SHARD_LIFE_RECOVERY;4", "ATTRIBUTE_SHARD_LIFE_RECOVERY;5", "ATTRIBUTE_SHARD_LIFE_RECOVERY;6", "ATTRIBUTE_SHARD_LIFE_RECOVERY;7", "ATTRIBUTE_SHARD_LIFE_RECOVERY;8", "ATTRIBUTE_SHARD_LIFE_RECOVERY;9", "ATTRIBUTE_SHARD_LIFE_RECOVERY;10"], "ATTRIBUTE_SHARD_EXPERIENCE;1": ["ATTRIBUTE_SHARD_EXPERIENCE;2", "ATTRIBUTE_SHARD_EXPERIENCE;3", "ATTRIBUTE_SHARD_EXPERIENCE;4", "ATTRIBUTE_SHARD_EXPERIENCE;5", "ATTRIBUTE_SHARD_EXPERIENCE;6", "ATTRIBUTE_SHARD_EXPERIENCE;7", "ATTRIBUTE_SHARD_EXPERIENCE;8", "ATTRIBUTE_SHARD_EXPERIENCE;9", "ATTRIBUTE_SHARD_EXPERIENCE;10"], "ATTRIBUTE_SHARD_DOMINANCE;1": ["ATTRIBUTE_SHARD_DOMINANCE;2", "ATTRIBUTE_SHARD_DOMINANCE;3", "ATTRIBUTE_SHARD_DOMINANCE;4", "ATTRIBUTE_SHARD_DOMINANCE;5", "ATTRIBUTE_SHARD_DOMINANCE;6", "ATTRIBUTE_SHARD_DOMINANCE;7", "ATTRIBUTE_SHARD_DOMINANCE;8", "ATTRIBUTE_SHARD_DOMINANCE;9", "ATTRIBUTE_SHARD_DOMINANCE;10"], "ATTRIBUTE_SHARD_MANA_STEAL;1": ["ATTRIBUTE_SHARD_MANA_STEAL;2", "ATTRIBUTE_SHARD_MANA_STEAL;3", "ATTRIBUTE_SHARD_MANA_STEAL;4", "ATTRIBUTE_SHARD_MANA_STEAL;5", "ATTRIBUTE_SHARD_MANA_STEAL;6", "ATTRIBUTE_SHARD_MANA_STEAL;7", "ATTRIBUTE_SHARD_MANA_STEAL;8", "ATTRIBUTE_SHARD_MANA_STEAL;9", "ATTRIBUTE_SHARD_MANA_STEAL;10"], "ATTRIBUTE_SHARD_VETERAN;1": ["ATTRIBUTE_SHARD_VETERAN;2", "ATTRIBUTE_SHARD_VETERAN;3", "ATTRIBUTE_SHARD_VETERAN;4", "ATTRIBUTE_SHARD_VETERAN;5", "ATTRIBUTE_SHARD_VETERAN;6", "ATTRIBUTE_SHARD_VETERAN;7", "ATTRIBUTE_SHARD_VETERAN;8", "ATTRIBUTE_SHARD_VETERAN;9", "ATTRIBUTE_SHARD_VETERAN;10"], "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;1": ["ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;2", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;3", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;4", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;5", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;6", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;7", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;8", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;9", "ATTRIBUTE_SHARD_ARACHNO_RESISTANCE;10"], "ATTRIBUTE_SHARD_MENDING;1": ["ATTRIBUTE_SHARD_MENDING;2", "ATTRIBUTE_SHARD_MENDING;3", "ATTRIBUTE_SHARD_MENDING;4", "ATTRIBUTE_SHARD_MENDING;5", "ATTRIBUTE_SHARD_MENDING;6", "ATTRIBUTE_SHARD_MENDING;7", "ATTRIBUTE_SHARD_MENDING;8", "ATTRIBUTE_SHARD_MENDING;9", "ATTRIBUTE_SHARD_MENDING;10"], "ATTRIBUTE_SHARD_ENDER_RESISTANCE;1": ["ATTRIBUTE_SHARD_ENDER_RESISTANCE;2", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;3", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;4", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;5", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;6", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;7", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;8", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;9", "ATTRIBUTE_SHARD_ENDER_RESISTANCE;10"], "ATTRIBUTE_SHARD_MANA_REGENERATION;1": ["ATTRIBUTE_SHARD_MANA_REGENERATION;2", "ATTRIBUTE_SHARD_MANA_REGENERATION;3", "ATTRIBUTE_SHARD_MANA_REGENERATION;4", "ATTRIBUTE_SHARD_MANA_REGENERATION;5", "ATTRIBUTE_SHARD_MANA_REGENERATION;6", "ATTRIBUTE_SHARD_MANA_REGENERATION;7", "ATTRIBUTE_SHARD_MANA_REGENERATION;8", "ATTRIBUTE_SHARD_MANA_REGENERATION;9", "ATTRIBUTE_SHARD_MANA_REGENERATION;10"], "ATTRIBUTE_SHARD_MAGIC_FIND;1": ["ATTRIBUTE_SHARD_MAGIC_FIND;2", "ATTRIBUTE_SHARD_MAGIC_FIND;3", "ATTRIBUTE_SHARD_MAGIC_FIND;4", "ATTRIBUTE_SHARD_MAGIC_FIND;5", "ATTRIBUTE_SHARD_MAGIC_FIND;6", "ATTRIBUTE_SHARD_MAGIC_FIND;7", "ATTRIBUTE_SHARD_MAGIC_FIND;8", "ATTRIBUTE_SHARD_MAGIC_FIND;9", "ATTRIBUTE_SHARD_MAGIC_FIND;10"], "ATTRIBUTE_SHARD_IGNITION;1": ["ATTRIBUTE_SHARD_IGNITION;2", "ATTRIBUTE_SHARD_IGNITION;3", "ATTRIBUTE_SHARD_IGNITION;4", "ATTRIBUTE_SHARD_IGNITION;5", "ATTRIBUTE_SHARD_IGNITION;6", "ATTRIBUTE_SHARD_IGNITION;7", "ATTRIBUTE_SHARD_IGNITION;8", "ATTRIBUTE_SHARD_IGNITION;9", "ATTRIBUTE_SHARD_IGNITION;10"], "ATTRIBUTE_SHARD_BLAZING;1": ["ATTRIBUTE_SHARD_BLAZING;2", "ATTRIBUTE_SHARD_BLAZING;3", "ATTRIBUTE_SHARD_BLAZING;4", "ATTRIBUTE_SHARD_BLAZING;5", "ATTRIBUTE_SHARD_BLAZING;6", "ATTRIBUTE_SHARD_BLAZING;7", "ATTRIBUTE_SHARD_BLAZING;8", "ATTRIBUTE_SHARD_BLAZING;9", "ATTRIBUTE_SHARD_BLAZING;10"], "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;1": ["ATTRIBUTE_SHARD_BLAZING_RESISTANCE;2", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;3", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;4", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;5", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;6", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;7", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;8", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;9", "ATTRIBUTE_SHARD_BLAZING_RESISTANCE;10"], "ATTRIBUTE_SHARD_DEADEYE;1": ["ATTRIBUTE_SHARD_DEADEYE;2", "ATTRIBUTE_SHARD_DEADEYE;3", "ATTRIBUTE_SHARD_DEADEYE;4", "ATTRIBUTE_SHARD_DEADEYE;5", "ATTRIBUTE_SHARD_DEADEYE;6", "ATTRIBUTE_SHARD_DEADEYE;7", "ATTRIBUTE_SHARD_DEADEYE;8", "ATTRIBUTE_SHARD_DEADEYE;9", "ATTRIBUTE_SHARD_DEADEYE;10"], "ATTRIBUTE_SHARD_LIFELINE;1": ["ATTRIBUTE_SHARD_LIFELINE;2", "ATTRIBUTE_SHARD_LIFELINE;3", "ATTRIBUTE_SHARD_LIFELINE;4", "ATTRIBUTE_SHARD_LIFELINE;5", "ATTRIBUTE_SHARD_LIFELINE;6", "ATTRIBUTE_SHARD_LIFELINE;7", "ATTRIBUTE_SHARD_LIFELINE;8", "ATTRIBUTE_SHARD_LIFELINE;9", "ATTRIBUTE_SHARD_LIFELINE;10"], "ATTRIBUTE_SHARD_FORTITUDE;1": ["ATTRIBUTE_SHARD_FORTITUDE;2", "ATTRIBUTE_SHARD_FORTITUDE;3", "ATTRIBUTE_SHARD_FORTITUDE;4", "ATTRIBUTE_SHARD_FORTITUDE;5", "ATTRIBUTE_SHARD_FORTITUDE;6", "ATTRIBUTE_SHARD_FORTITUDE;7", "ATTRIBUTE_SHARD_FORTITUDE;8", "ATTRIBUTE_SHARD_FORTITUDE;9", "ATTRIBUTE_SHARD_FORTITUDE;10"], "ATTRIBUTE_SHARD_WARRIOR;1": ["ATTRIBUTE_SHARD_WARRIOR;2", "ATTRIBUTE_SHARD_WARRIOR;3", "ATTRIBUTE_SHARD_WARRIOR;4", "ATTRIBUTE_SHARD_WARRIOR;5", "ATTRIBUTE_SHARD_WARRIOR;6", "ATTRIBUTE_SHARD_WARRIOR;7", "ATTRIBUTE_SHARD_WARRIOR;8", "ATTRIBUTE_SHARD_WARRIOR;9", "ATTRIBUTE_SHARD_WARRIOR;10"], "RIFT_TROPHY_WYLDLY_SUPREME": ["RIFT_TROPHY_CHICKEN_N_EGG", "RIFT_TROPHY_MIRRORED", "RIFT_TROPHY_CITIZEN", "RIFT_TROPHY_LAZY_LIVING", "RIFT_TROPHY_SLIME", "RIFT_TROPHY_VAMPIRIC", "RIFT_TROPHY_MOUNTAIN"], "CENTURY_RING": ["CENTURY_TALISMAN"], "SMALL_BRAIN;1": ["SMALL_BRAIN;2", "SMALL_BRAIN;3"], "SEAL_RING": ["SEAL_TALISMAN"], "TIDAL;1": ["TIDAL;2", "TIDAL;3"]}