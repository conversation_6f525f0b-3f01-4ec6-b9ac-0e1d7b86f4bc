{"leveling_xp": [50, 125, 200, 300, 500, 750, 1000, 1500, 2000, 3500, 5000, 7500, 10000, 15000, 20000, 30000, 50000, 75000, 100000, 200000, 300000, 400000, 500000, 600000, 700000, 800000, 900000, 1000000, 1100000, 1200000, 1300000, 1400000, 1500000, 1600000, 1700000, 1800000, 1900000, 2000000, 2100000, 2200000, 2300000, 2400000, 2500000, 2600000, 2750000, 2900000, 3100000, 3400000, 3700000, 4000000, 4300000, 4600000, 4900000, 5200000, 5500000, 5800000, 6100000, 6400000, 6700000, 7000000], "leveling_caps": {"taming": 50, "mining": 60, "foraging": 50, "enchanting": 60, "carpentry": 50, "farming": 50, "combat": 60, "fishing": 50, "alchemy": 50, "runecrafting": 25, "catacombs": 50, "HOTM": 10, "social": 25}, "runecrafting_xp": [50, 100, 125, 160, 200, 250, 315, 400, 500, 625, 785, 1000, 1250, 1600, 2000, 2465, 3125, 4000, 5000, 6200, 7800, 9800, 12200, 15300, 19050], "slayer_xp": {"zombie": [5, 15, 200, 1000, 5000, 20000, 100000, 400000, 1000000], "spider": [5, 15, 200, 1000, 5000, 20000, 100000, 400000, 1000000], "wolf": [10, 30, 250, 1500, 5000, 20000, 100000, 400000, 1000000], "enderman": [10, 30, 250, 1500, 5000, 20000, 100000, 400000, 1000000], "blaze": [10, 30, 250, 1500, 5000, 20000, 100000, 400000, 1000000], "vampire": [20, 75, 240, 840, 2400]}, "slayer_boss_xp": [5, 25, 100, 500, 1500], "slayer_boss_xp_type": {"vampire": [10, 25, 60, 120, 150]}, "slayer_highest_tier": {"Revenant Horror": 5, "Tarantula Broodfather": 4, "Sven Packmaster": 4, "Voidgloom Seraph": 4, "Inferno Demonlord": 4, "Riftstalker Bloodfiend": 5}, "fancy_name_to_slayer": {"Revenant Horror": "zombie", "Tarantula Broodfather": "spider", "Sven Packmaster": "wolf", "Voidgloom Seraph": "enderman", "Inferno Demonlord": "blaze", "Riftstalker Bloodfiend": "vampire"}, "slayer_to_highest_tier": {"zombie": 5, "spider": 4, "wolf": 4, "enderman": 4, "blaze": 4, "vampire": 5}, "slayer_tier_colors": ["§a", "§e", "§c", "§4", "§5"], "rng_meter_dungeon_score": {"§b§lS+": 300, "§6§lS": 189}, "catacombs": [50, 75, 110, 160, 230, 330, 470, 670, 950, 1340, 1890, 2665, 3760, 5260, 7380, 10300, 14400, 20000, 27600, 38000, 52500, 71500, 97000, 132000, 180000, 243000, 328000, 445000, 600000, 800000, 1065000, 1410000, 1900000, 2500000, 3300000, 4300000, 5600000, 7200000, 9200000, 12000000, 15000000, 19000000, 24000000, 30000000, 38000000, 48000000, 60000000, 75000000, 93000000, 116250000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000, 200000000], "HOTM": [0, 3000, 9000, 25000, 60000, 100000, 150000, 210000, 290000, 400000], "social": [50, 100, 150, 250, 500, 750, 1000, 1250, 1500, 2000, 2500, 3000, 3750, 4500, 6000, 8000, 10000, 12500, 15000, 20000, 25000, 30000, 35000, 40000, 50000], "bestiary": {"ISLAND": [10, 15, 75, 150, 250], "BOSS": [2, 3, 5, 10, 10, 10, 10, 25, 25, 50, 50, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "MOB": [10, 15, 75, 150, 250, 500, 1500, 2500, 5000, 15000, 25000, 50000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000, 100000], "caps": {"ISLAND": 500, "BOSS": 3200, "MOB": 3000000}}}