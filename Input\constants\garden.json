{"garden_exp": [0, 70, 70, 140, 240, 600, 1500, 2000, 2500, 3000, 10000, 10000, 10000, 10000, 10000], "crop_milestones": {"WHEAT": [30, 50, 80, 170, 330, 670, 1330, 2500, 3500, 5000, 6500, 8000, 10000, 20000, 35000, 50000, 75000, 100000, 175000, 250000, 350000, 500000, 750000, 1000000, 1300000, 1600000, 2000000, 2300000, 2600000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000], "CARROT": [100, 150, 250, 500, 1500, 2500, 5000, 7500, 10000, 15000, 20000, 25000, 40000, 70000, 100000, 200000, 250000, 250000, 500000, 750000, 1000000, 1500000, 2000000, 3000000, 4000000, 5000000, 6000000, 7000000, 8000000, 9000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000], "POTATO": [100, 150, 250, 500, 1500, 2500, 5000, 7500, 10000, 15000, 20000, 25000, 40000, 70000, 100000, 200000, 250000, 250000, 500000, 750000, 1000000, 1500000, 2000000, 3000000, 4000000, 5000000, 6000000, 7000000, 8000000, 9000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000, 10000000], "MELON": [150, 250, 400, 850, 1650, 3350, 6650, 12500, 17500, 25000, 32500, 40000, 50000, 100000, 175000, 250000, 375000, 500000, 875000, 1250000, 1750000, 2500000, 3750000, 5000000, 6500000, 8000000, 10000000, 11500000, 13000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000, 15000000], "PUMPKIN": [30, 50, 80, 170, 330, 670, 1330, 2500, 3500, 5000, 6500, 8000, 10000, 20000, 35000, 50000, 75000, 100000, 175000, 250000, 350000, 500000, 750000, 1000000, 1300000, 1600000, 2000000, 2300000, 2600000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000], "SUGAR_CANE": [60, 100, 160, 340, 660, 1340, 2660, 5000, 7000, 10000, 13000, 16000, 20000, 40000, 70000, 100000, 150000, 200000, 350000, 500000, 700000, 1000000, 1500000, 2000000, 2600000, 3200000, 4000000, 4600000, 5200000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000], "COCOA_BEANS": [90, 150, 250, 500, 1000, 2000, 4000, 7500, 10000, 15000, 20000, 25000, 30000, 50000, 100000, 150000, 200000, 300000, 500000, 750000, 1000000, 1500000, 2000000, 3000000, 4000000, 5000000, 6000000, 7000000, 8000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000], "CACTUS": [60, 100, 160, 340, 660, 1340, 2660, 5000, 7000, 10000, 13000, 16000, 20000, 40000, 70000, 100000, 150000, 200000, 350000, 500000, 700000, 1000000, 1500000, 2000000, 2600000, 3200000, 4000000, 4600000, 5200000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000, 6000000], "MUSHROOM": [30, 50, 80, 170, 330, 670, 1330, 2500, 3500, 5000, 6500, 8000, 10000, 20000, 35000, 50000, 75000, 100000, 175000, 250000, 350000, 500000, 750000, 1000000, 1300000, 1600000, 2000000, 2300000, 2600000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000, 3000000], "NETHER_WART": [90, 150, 250, 500, 1000, 2000, 4000, 7500, 10000, 15000, 20000, 25000, 30000, 50000, 100000, 150000, 200000, 300000, 500000, 750000, 1000000, 1500000, 2000000, 3000000, 4000000, 5000000, 6000000, 7000000, 8000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000, 9000000]}, "visitors": {"adventurer": "UNCOMMON", "alchemist": "UNCOMMON", "andrew": "UNCOMMON", "anita": "UNCOMMON", "arthur": "UNCOMMON", "baker": "LEGENDARY", "banker_broadjaw": "UNCOMMON", "bartender": "RARE", "bear_pete": "RARE", "beth": "LEGENDARY", "dalbrek": "RARE", "disguised_rats": "LEGENDARY", "duke": "UNCOMMON", "dusk": "UNCOMMON", "emissary_carlton": "UNCOMMON", "emissary_ceanna": "UNCOMMON", "emissary_fraiser": "UNCOMMON", "emissary_sisko": "RARE", "emissary_wilson": "UNCOMMON", "farmer_jon": "UNCOMMON", "farmhand": "UNCOMMON", "fear_mongerer": "UNCOMMON", "felix": "UNCOMMON", "fisherman": "UNCOMMON", "fragilis": "RARE", "friendly_hiker": "UNCOMMON", "geonathan_greatforge": "UNCOMMON", "gimley": "UNCOMMON", "gold_forger": "RARE", "grandma_wolf": "RARE", "guy": "UNCOMMON", "gwendolyn": "RARE", "hornum": "UNCOMMON", "hungry_hiker": "UNCOMMON", "iron_forger": "RARE", "jack": "UNCOMMON", "jacob": "UNCOMMON", "jamie": "UNCOMMON", "jerry": "LEGENDARY", "jotraeline_greatforge": "UNCOMMON", "lazy_miner": "RARE", "leo": "UNCOMMON", "liam": "UNCOMMON", "librarian": "UNCOMMON", "lumberjack": "UNCOMMON", "lumina": "RARE", "lynn": "UNCOMMON", "madame_eleanor": "LEGENDARY", "maeve": "MYTHIC", "mason": "UNCOMMON", "odawa": "UNCOMMON", "old_man_garry": "RARE", "oringo": "UNCOMMON", "pest_wrangler": "UNCOMMON", "plumber_joe": "UNCOMMON", "puzzler": "RARE", "queen_mismyla": "RARE", "ravenous_rhino": "MYTHIC", "rhys": "UNCOMMON", "royal_resident_neighbour": "UNCOMMON", "royal_resident_peasant": "UNCOMMON", "royal_resident_reward": "RARE", "rusty": "RARE", "ryu": "UNCOMMON", "sargwyn": "UNCOMMON", "seraphine": "LEGENDARY", "seymour": "RARE", "shaggy": "UNCOMMON", "shifty": "RARE", "sirius": "LEGENDARY", "spaceman": "SPECIAL", "stella": "UNCOMMON", "tammy": "RARE", "tarwen": "UNCOMMON", "terry": "UNCOMMON", "tia": "RARE", "tom": "UNCOMMON", "trevor": "UNCOMMON", "vex": "UNCOMMON", "vinyl_collector": "RARE", "weaponsmith": "UNCOMMON", "wizard": "UNCOMMON", "xalx": "UNCOMMON", "zog": "RARE"}, "plots": {"beginner_1": {"name": "§aPlot §b2", "x": 1, "y": 2}, "beginner_2": {"name": "§aPlot §b1", "x": 2, "y": 1}, "beginner_3": {"name": "§aPlot §b4", "x": 2, "y": 3}, "beginner_4": {"name": "§aPlot §b3", "x": 3, "y": 2}, "intermediate_1": {"name": "§aPlot §b5", "x": 1, "y": 1}, "intermediate_2": {"name": "§aPlot §b7", "x": 1, "y": 3}, "intermediate_3": {"name": "§aPlot §b6", "x": 3, "y": 1}, "intermediate_4": {"name": "§aPlot §b8", "x": 3, "y": 3}, "advanced_1": {"name": "§aPlot §b15", "x": 0, "y": 1}, "advanced_2": {"name": "§aPlot §b10", "x": 0, "y": 2}, "advanced_3": {"name": "§aPlot §b17", "x": 0, "y": 3}, "advanced_4": {"name": "§aPlot §b13", "x": 1, "y": 0}, "advanced_5": {"name": "§aPlot §b19", "x": 1, "y": 4}, "advanced_6": {"name": "§aPlot §b9", "x": 2, "y": 0}, "advanced_7": {"name": "§aPlot §b12", "x": 2, "y": 4}, "advanced_8": {"name": "§aPlot §b14", "x": 3, "y": 0}, "advanced_9": {"name": "§aPlot §b20", "x": 3, "y": 4}, "advanced_10": {"name": "§aPlot §b16", "x": 4, "y": 1}, "advanced_11": {"name": "§aPlot §b11", "x": 4, "y": 2}, "advanced_12": {"name": "§aPlot §b18", "x": 4, "y": 3}, "expert_1": {"name": "§aPlot §b21", "x": 0, "y": 0}, "expert_2": {"name": "§aPlot §b23", "x": 0, "y": 4}, "expert_3": {"name": "§aPlot §b22", "x": 4, "y": 0}, "expert_4": {"name": "§aPlot §b24", "x": 4, "y": 4}}, "plot_costs": {"beginner": [{"item": "COMPOST", "amount": 1}, {"item": "COMPOST", "amount": 2}, {"item": "COMPOST", "amount": 4}, {"item": "COMPOST", "amount": 8}], "intermediate": [{"item": "COMPOST", "amount": 16}, {"item": "COMPOST", "amount": 24}, {"item": "COMPOST", "amount": 32}, {"item": "COMPOST", "amount": 48}], "advanced": [{"item": "COMPOST", "amount": 64}, {"item": "COMPOST", "amount": 96}, {"item": "COMPOST", "amount": 128}, {"item": "ENCHANTED_COMPOST", "amount": 1}, {"item": "ENCHANTED_COMPOST", "amount": 1}, {"item": "ENCHANTED_COMPOST", "amount": 2}, {"item": "ENCHANTED_COMPOST", "amount": 2}, {"item": "ENCHANTED_COMPOST", "amount": 3}, {"item": "ENCHANTED_COMPOST", "amount": 3}, {"item": "ENCHANTED_COMPOST", "amount": 4}, {"item": "ENCHANTED_COMPOST", "amount": 5}, {"item": "ENCHANTED_COMPOST", "amount": 7}], "expert": [{"item": "ENCHANTED_COMPOST", "amount": 8}, {"item": "ENCHANTED_COMPOST", "amount": 10}, {"item": "ENCHANTED_COMPOST", "amount": 12}, {"item": "ENCHANTED_COMPOST", "amount": 15}]}, "barn": {"default_1": {"name": "§fDefault", "item": "WOOD-5"}, "default_2": {"name": "§aRed", "item": "QUARTZ_BLOCK"}, "default_3": {"name": "§aMedieval", "item": "LOG-1"}, "default_4": {"name": "§aSunny", "item": "RED_SANDSTONE"}, "cabin": {"name": "§9Cabin", "item": "STAINED_CLAY-3"}, "mansion_heights": {"name": "§5Mansion Heights", "item": "WOOD-1"}, "cube": {"name": "§aCube", "item": "CUBE_BARN_SKIN"}, "cozy_cottage": {"name": "§aCozy Cottage", "item": "COZY_COTTAGE_BARN_SKIN"}, "tavern": {"name": "§aTavern", "item": "TAVERN_BARN_SKIN"}, "windmill": {"name": "§aWindmill", "item": "WINDMILL_BARN_SKIN"}, "melon": {"name": "§6Melon", "item": "MELON_BARN_SKIN"}, "lucky": {"name": "§6Lucky", "item": "LUCKY_BARN_SKIN"}, "trading_post": {"name": "§aTrading Post", "item": "TRADING_POST_BARN_SKIN"}, "autumn_hut": {"name": "§aAutumn Hut", "item": "AUTUMN_HUT_BARN_SKIN"}, "bamboo": {"name": "§5Bamboo", "item": "BAMBOO_BARN_SKIN"}, "hive": {"name": "§6Hive", "item": "HIVE_BARN_SKIN"}, "castle": {"name": "§5Castle", "item": "CASTLE_BARN_SKIN"}, "frog": {"name": "§6Frog", "item": "FROG_BARN_SKIN"}, "jerry": {"name": "§6<PERSON><PERSON>", "item": "JERRY_BARN_SKIN"}, "pinwheel_house": {"name": "§6Pinwheel House", "item": "PINWHEEL_HOUSE_BARN_SKIN"}, "mushroom": {"name": "§6Mushroom", "item": "MUSHROOM_BARN_SKIN"}, "end": {"name": "§6The End", "item": "END_BARN_SKIN"}, "town_hall": {"name": "§6Town Hall", "item": "TOWN_HALL_BARN_SKIN"}, "winter_homestead": {"name": "§9Winter Homestead", "item": "WINTER_HOMESTEAD_BARN_SKIN"}, "enchanted_nook": {"name": "§9Enchanted Nook", "item": "ENCHANTED_NOOK_BARN_SKIN"}, "chocolate_factory": {"name": "§6Chocolate Factory", "item": "CHOCOLATE_FACTORY_BARN_SKIN"}, "sand_castle": {"name": "§6Sand Castle", "item": "SAND_CASTLE_BARN_SKIN"}, "pesthunters_lair": {"name": "§6Pesthunter's Lair", "item": "PESTHUNTERS_LAIR_BARN_SKIN"}, "winter_barn": {"name": "§aWinter Barn", "item": "WINTER_BARN_BARN_SKIN"}, "beach_ball": {"name": "§6Beach Ball", "item": "BEACH_BALL_BARN_SKIN"}, "main_street": {"name": "§6Main Street", "item": "MAIN_STREET_BARN_SKIN"}}, "crop_upgrades": [5, 10, 20, 50, 100, 500, 1000, 2000, 4000], "composter_upgrades": {"speed": {"1": {"upgrade": 20, "items": {"enchanted_wheat": 128}, "copper": 100}, "2": {"upgrade": 40, "items": {"enchanted_golden_carrot": 2}, "copper": 150}, "3": {"upgrade": 60, "items": {"enchanted_wheat": 256}, "copper": 200}, "4": {"upgrade": 80, "items": {"enchanted_golden_carrot": 4}, "copper": 250}, "5": {"upgrade": 100, "items": {"enchanted_wheat": 512}, "copper": 300}, "6": {"upgrade": 120, "items": {"enchanted_golden_carrot": 8}, "copper": 350}, "7": {"upgrade": 140, "items": {"enchanted_hay_bale": 8}, "copper": 400}, "8": {"upgrade": 160, "items": {"enchanted_golden_carrot": 16, "cropie": 3}, "copper": 500}, "9": {"upgrade": 180, "items": {"enchanted_hay_bale": 16, "cropie": 6}, "copper": 600}, "10": {"upgrade": 200, "items": {"enchanted_golden_carrot": 32, "cropie": 12}, "copper": 700}, "11": {"upgrade": 220, "items": {"enchanted_hay_bale": 24, "cropie": 32}, "copper": 800}, "12": {"upgrade": 240, "items": {"enchanted_golden_carrot": 48, "cropie": 64}, "copper": 900}, "13": {"upgrade": 260, "items": {"enchanted_hay_bale": 32, "cropie": 128}, "copper": 1000}, "14": {"upgrade": 280, "items": {"enchanted_golden_carrot": 64, "squash": 3}, "copper": 1200}, "15": {"upgrade": 300, "items": {"enchanted_hay_bale": 48, "squash": 6}, "copper": 1400}, "16": {"upgrade": 320, "items": {"enchanted_golden_carrot": 96, "squash": 12}, "copper": 1600}, "17": {"upgrade": 340, "items": {"enchanted_hay_bale": 64, "squash": 32}, "copper": 1800}, "18": {"upgrade": 360, "items": {"enchanted_golden_carrot": 128, "squash": 64}, "copper": 2000}, "19": {"upgrade": 380, "items": {"enchanted_hay_bale": 80, "squash": 128}, "copper": 2250}, "20": {"upgrade": 400, "items": {"enchanted_golden_carrot": 192, "fermento": 3}, "copper": 2500}, "21": {"upgrade": 420, "items": {"enchanted_hay_bale": 104, "fermento": 6}, "copper": 2750}, "22": {"upgrade": 440, "items": {"enchanted_golden_carrot": 256, "fermento": 12}, "copper": 3000}, "23": {"upgrade": 460, "items": {"enchanted_hay_bale": 128, "condensed_fermento": 4}, "copper": 3300}, "24": {"upgrade": 480, "items": {"enchanted_golden_carrot": 320, "condensed_fermento": 7}, "copper": 3600}, "25": {"upgrade": 500, "items": {"enchanted_hay_bale": 160, "condensed_fermento": 14}, "copper": 4000}}, "multi_drop": {"1": {"upgrade": 3, "items": {"enchanted_baked_potato": 1}, "copper": 100}, "2": {"upgrade": 6, "items": {"enchanted_pumpkin": 64}, "copper": 150}, "3": {"upgrade": 9, "items": {"enchanted_baked_potato": 2}, "copper": 200}, "4": {"upgrade": 12, "items": {"polished_pumpkin": 1}, "copper": 250}, "5": {"upgrade": 15, "items": {"enchanted_baked_potato": 4}, "copper": 300}, "6": {"upgrade": 18, "items": {"polished_pumpkin": 2}, "copper": 350}, "7": {"upgrade": 21, "items": {"enchanted_baked_potato": 8}, "copper": 400}, "8": {"upgrade": 24, "items": {"polished_pumpkin": 4, "cropie": 3}, "copper": 500}, "9": {"upgrade": 27, "items": {"enchanted_baked_potato": 16, "cropie": 6}, "copper": 600}, "10": {"upgrade": 30, "items": {"polished_pumpkin": 8, "cropie": 12}, "copper": 700}, "11": {"upgrade": 33, "items": {"enchanted_baked_potato": 32, "cropie": 32}, "copper": 800}, "12": {"upgrade": 36, "items": {"polished_pumpkin": 16, "cropie": 64}, "copper": 900}, "13": {"upgrade": 39, "items": {"enchanted_baked_potato": 48, "cropie": 128}, "copper": 1000}, "14": {"upgrade": 42, "items": {"polished_pumpkin": 32, "squash": 3}, "copper": 1200}, "15": {"upgrade": 45, "items": {"enchanted_baked_potato": 64, "squash": 6}, "copper": 1400}, "16": {"upgrade": 48, "items": {"polished_pumpkin": 48, "squash": 12}, "copper": 1600}, "17": {"upgrade": 51, "items": {"enchanted_baked_potato": 96, "squash": 32}, "copper": 1800}, "18": {"upgrade": 54, "items": {"polished_pumpkin": 64, "squash": 64}, "copper": 2000}, "19": {"upgrade": 57, "items": {"enchanted_baked_potato": 128, "squash": 128}, "copper": 2250}, "20": {"upgrade": 60, "items": {"polished_pumpkin": 96, "fermento": 3}, "copper": 2500}, "21": {"upgrade": 63, "items": {"enchanted_baked_potato": 192, "fermento": 6}, "copper": 2750}, "22": {"upgrade": 66, "items": {"polished_pumpkin": 128, "fermento": 12}, "copper": 3000}, "23": {"upgrade": 69, "items": {"enchanted_baked_potato": 256, "condensed_fermento": 4}, "copper": 3300}, "24": {"upgrade": 72, "items": {"polished_pumpkin": 192, "condensed_fermento": 7}, "copper": 3600}, "25": {"upgrade": 75, "items": {"enchanted_baked_potato": 320, "condensed_fermento": 14}, "copper": 4000}}, "fuel_cap": {"1": {"upgrade": 130000, "items": {"enchanted_sugar_cane": 1}, "copper": 100}, "2": {"upgrade": 160000, "items": {"enchanted_melon_block": 4}, "copper": 150}, "3": {"upgrade": 190000, "items": {"enchanted_sugar_cane": 2}, "copper": 200}, "4": {"upgrade": 220000, "items": {"enchanted_melon_block": 8}, "copper": 250}, "5": {"upgrade": 250000, "items": {"enchanted_sugar_cane": 4}, "copper": 300}, "6": {"upgrade": 280000, "items": {"enchanted_melon_block": 16}, "copper": 350}, "7": {"upgrade": 310000, "items": {"enchanted_sugar_cane": 8}, "copper": 400}, "8": {"upgrade": 340000, "items": {"enchanted_melon_block": 32, "cropie": 3}, "copper": 500}, "9": {"upgrade": 370000, "items": {"enchanted_sugar_cane": 16, "cropie": 6}, "copper": 600}, "10": {"upgrade": 400000, "items": {"enchanted_melon_block": 48, "cropie": 12}, "copper": 700}, "11": {"upgrade": 430000, "items": {"enchanted_sugar_cane": 32, "cropie": 32}, "copper": 800}, "12": {"upgrade": 460000, "items": {"enchanted_melon_block": 64, "cropie": 64}, "copper": 900}, "13": {"upgrade": 490000, "items": {"enchanted_sugar_cane": 48, "cropie": 128}, "copper": 1000}, "14": {"upgrade": 520000, "items": {"enchanted_melon_block": 96, "squash": 3}, "copper": 1200}, "15": {"upgrade": 550000, "items": {"enchanted_sugar_cane": 64, "squash": 6}, "copper": 1400}, "16": {"upgrade": 580000, "items": {"enchanted_melon_block": 128, "squash": 12}, "copper": 1600}, "17": {"upgrade": 610000, "items": {"enchanted_sugar_cane": 96, "squash": 32}, "copper": 1800}, "18": {"upgrade": 640000, "items": {"enchanted_melon_block": 192, "squash": 64}, "copper": 2000}, "19": {"upgrade": 670000, "items": {"enchanted_sugar_cane": 128, "squash": 128}, "copper": 2250}, "20": {"upgrade": 700000, "items": {"enchanted_melon_block": 256, "fermento": 3}, "copper": 2500}, "21": {"upgrade": 730000, "items": {"enchanted_sugar_cane": 192, "fermento": 6}, "copper": 2750}, "22": {"upgrade": 760000, "items": {"enchanted_melon_block": 384, "fermento": 12}, "copper": 3000}, "23": {"upgrade": 790000, "items": {"enchanted_sugar_cane": 256, "condensed_fermento": 4}, "copper": 3300}, "24": {"upgrade": 820000, "items": {"enchanted_melon_block": 512, "condensed_fermento": 7}, "copper": 3600}, "25": {"upgrade": 850000, "items": {"enchanted_sugar_cane": 320, "condensed_fermento": 14}, "copper": 4000}}, "organic_matter_cap": {"1": {"upgrade": 60000, "items": {"enchanted_cactus": 1}, "copper": 100}, "2": {"upgrade": 80000, "items": {"enchanted_cookie": 3}, "copper": 150}, "3": {"upgrade": 100000, "items": {"enchanted_cactus": 2}, "copper": 200}, "4": {"upgrade": 120000, "items": {"enchanted_cookie": 6}, "copper": 250}, "5": {"upgrade": 140000, "items": {"enchanted_cactus": 4}, "copper": 300}, "6": {"upgrade": 160000, "items": {"enchanted_cookie": 12}, "copper": 350}, "7": {"upgrade": 180000, "items": {"enchanted_cactus": 7}, "copper": 400}, "8": {"upgrade": 200000, "items": {"enchanted_cookie": 24, "cropie": 3}, "copper": 500}, "9": {"upgrade": 220000, "items": {"enchanted_cactus": 10, "cropie": 6}, "copper": 600}, "10": {"upgrade": 240000, "items": {"enchanted_cookie": 48, "cropie": 12}, "copper": 700}, "11": {"upgrade": 260000, "items": {"enchanted_cactus": 16, "cropie": 32}, "copper": 800}, "12": {"upgrade": 280000, "items": {"enchanted_cookie": 72, "cropie": 64}, "copper": 900}, "13": {"upgrade": 300000, "items": {"enchanted_cactus": 24, "cropie": 128}, "copper": 1000}, "14": {"upgrade": 320000, "items": {"enchanted_cookie": 96, "squash": 3}, "copper": 1200}, "15": {"upgrade": 340000, "items": {"enchanted_cactus": 32, "squash": 6}, "copper": 1400}, "16": {"upgrade": 360000, "items": {"enchanted_cookie": 144, "squash": 12}, "copper": 1600}, "17": {"upgrade": 380000, "items": {"enchanted_cactus": 48, "squash": 32}, "copper": 1800}, "18": {"upgrade": 400000, "items": {"enchanted_cookie": 192, "squash": 64}, "copper": 2000}, "19": {"upgrade": 420000, "items": {"enchanted_cactus": 64, "squash": 128}, "copper": 2250}, "20": {"upgrade": 440000, "items": {"enchanted_cookie": 256, "fermento": 3}, "copper": 2500}, "21": {"upgrade": 460000, "items": {"enchanted_cactus": 96, "fermento": 6}, "copper": 2750}, "22": {"upgrade": 480000, "items": {"enchanted_cookie": 352, "fermento": 12}, "copper": 3000}, "23": {"upgrade": 500000, "items": {"enchanted_cactus": 128, "condensed_fermento": 4}, "copper": 3300}, "24": {"upgrade": 520000, "items": {"enchanted_cookie": 448, "condensed_fermento": 7}, "copper": 3600}, "25": {"upgrade": 540000, "items": {"enchanted_cactus": 160, "condensed_fermento": 14}, "copper": 4000}}, "cost_reduction": {"1": {"upgrade": 1, "items": {"enchanted_brown_mushroom": 32}, "copper": 100}, "2": {"upgrade": 2, "items": {"mutant_nether_stalk": 1}, "copper": 150}, "3": {"upgrade": 3, "items": {"ENCHANTED_HUGE_MUSHROOM_2": 2}, "copper": 200}, "4": {"upgrade": 4, "items": {"mutant_nether_stalk": 2}, "copper": 250}, "5": {"upgrade": 5, "items": {"ENCHANTED_HUGE_MUSHROOM_1": 4}, "copper": 300}, "6": {"upgrade": 6, "items": {"mutant_nether_stalk": 4}, "copper": 350}, "7": {"upgrade": 7, "items": {"ENCHANTED_HUGE_MUSHROOM_2": 16}, "copper": 400}, "8": {"upgrade": 8, "items": {"mutant_nether_stalk": 8, "cropie": 3}, "copper": 500}, "9": {"upgrade": 9, "items": {"ENCHANTED_HUGE_MUSHROOM_1": 32, "cropie": 6}, "copper": 600}, "10": {"upgrade": 10, "items": {"mutant_nether_stalk": 16, "cropie": 12}, "copper": 700}, "11": {"upgrade": 11, "items": {"ENCHANTED_HUGE_MUSHROOM_2": 64, "cropie": 32}, "copper": 800}, "12": {"upgrade": 12, "items": {"mutant_nether_stalk": 32, "cropie": 64}, "copper": 900}, "13": {"upgrade": 13, "items": {"ENCHANTED_HUGE_MUSHROOM_1": 128, "cropie": 128}, "copper": 1000}, "14": {"upgrade": 14, "items": {"mutant_nether_stalk": 48, "squash": 3}, "copper": 1200}, "15": {"upgrade": 15, "items": {"ENCHANTED_HUGE_MUSHROOM_2": 256, "squash": 6}, "copper": 1400}, "16": {"upgrade": 16, "items": {"mutant_nether_stalk": 64, "squash": 12}, "copper": 1600}, "17": {"upgrade": 17, "items": {"ENCHANTED_HUGE_MUSHROOM_1": 448, "squash": 32}, "copper": 1800}, "18": {"upgrade": 18, "items": {"mutant_nether_stalk": 96, "squash": 64}, "copper": 2000}, "19": {"upgrade": 19, "items": {"ENCHANTED_HUGE_MUSHROOM_2": 640, "squash": 128}, "copper": 2250}, "20": {"upgrade": 20, "items": {"mutant_nether_stalk": 160, "fermento": 3}, "copper": 2500}, "21": {"upgrade": 21, "items": {"ENCHANTED_HUGE_MUSHROOM_1": 832, "fermento": 6}, "copper": 2750}, "22": {"upgrade": 22, "items": {"mutant_nether_stalk": 224, "fermento": 12}, "copper": 3000}, "23": {"upgrade": 23, "items": {"ENCHANTED_HUGE_MUSHROOM_2": 1024, "condensed_fermento": 4}, "copper": 3300}, "24": {"upgrade": 24, "items": {"mutant_nether_stalk": 288, "condensed_fermento": 7}, "copper": 3600}, "25": {"upgrade": 25, "items": {"ENCHANTED_HUGE_MUSHROOM_1": 1216, "condensed_fermento": 14}, "copper": 4000}}}, "composter_tooltips": {"speed": "§7Increases the speed of the composter by §a{}%§7.", "multi_drop": "§a{}% §7Chance to produce extra compost.", "fuel_cap": "§7Maximum fuel capacity: §a{}§7.", "organic_matter_cap": "§7Maximum organic matter: §a{}§7.", "cost_reduction": "§7Reduces the cost of composting by §a{}%§7."}}